package com.example.demo;

import java.util.List;

/**
 * DocumentRecognitionService类的简单测试
 */
public class DocumentRecognitionServiceTest {

    public static void main(String[] args) {
        DocumentRecognitionServiceTest test = new DocumentRecognitionServiceTest();
        test.runAllTests();
    }

    public void runAllTests() {
        System.out.println("开始运行DocumentRecognitionService测试...");

        testRecognizeDocumentType_PDF();
        testRecognizeDocumentType_Word();
        testRecognizeDocumentType_Image();
        testRecognizeDocumentType_Unknown();
        testExtractContent();
        testAnalyzeStructure();

        System.out.println("所有DocumentRecognitionService测试通过！");
    }

    public void testRecognizeDocumentType_PDF() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        String result = service.recognizeDocumentType("test.pdf");
        if (!"PDF文档".equals(result)) {
            throw new RuntimeException("应该正确识别PDF文档，实际结果: " + result);
        }
        System.out.println("✓ testRecognizeDocumentType_PDF 通过");
    }

    public void testRecognizeDocumentType_Word() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        String result = service.recognizeDocumentType("test.docx");
        if (!"Word文档".equals(result)) {
            throw new RuntimeException("应该正确识别Word文档，实际结果: " + result);
        }
        System.out.println("✓ testRecognizeDocumentType_Word 通过");
    }

    public void testRecognizeDocumentType_Image() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        String result = service.recognizeDocumentType("test.jpg");
        if (!"图片文档".equals(result)) {
            throw new RuntimeException("应该正确识别图片文档，实际结果: " + result);
        }
        System.out.println("✓ testRecognizeDocumentType_Image 通过");
    }

    public void testRecognizeDocumentType_Unknown() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        String result = service.recognizeDocumentType("test.txt");
        if (!"未知类型".equals(result)) {
            throw new RuntimeException("应该正确识别未知类型，实际结果: " + result);
        }
        System.out.println("✓ testRecognizeDocumentType_Unknown 通过");
    }

    public void testExtractContent() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        List<String> content = service.extractContent("test.pdf");
        if (content == null) {
            throw new RuntimeException("提取的内容不应为null");
        }
        if (content.isEmpty()) {
            throw new RuntimeException("提取的内容不应为空");
        }
        System.out.println("✓ testExtractContent 通过");
    }

    public void testAnalyzeStructure() {
        DocumentRecognitionService service = new DocumentRecognitionService();
        String result = service.analyzeStructure("test.pdf");
        if (result == null) {
            throw new RuntimeException("结构分析结果不应为null");
        }
        if (result.isEmpty()) {
            throw new RuntimeException("结构分析结果不应为空");
        }
        System.out.println("✓ testAnalyzeStructure 通过");
    }
}
