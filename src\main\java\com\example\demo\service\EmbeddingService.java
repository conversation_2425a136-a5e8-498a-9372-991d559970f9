package com.example.demo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 嵌入向量服务
 * 用于计算文本的语义相似度
 */
public class EmbeddingService {

    private static final Logger logger = LoggerFactory.getLogger(EmbeddingService.class);

    // 缓存计算过的向量
    private final Map<String, float[]> embeddingCache = new ConcurrentHashMap<>();

    // 增强版语义关键词库（模拟BERT语义理解）
    private static final String[] SEMANTIC_KEYWORDS = {
            // 设备类型
            "阀门", "调压阀", "球阀", "蝶阀", "闸阀", "安全阀", "电磁阀",
            "泵", "增压泵", "循环泵", "离心泵", "螺杆泵", "齿轮泵",
            "过滤器", "滤芯", "净化器", "分离器", "除尘器",
            "加热器", "换热器", "冷却器", "蒸发器", "冷凝器",
            "储罐", "容器", "反应器", "塔器", "分离塔",
            "压缩机", "风机", "鼓风机", "引风机", "排风机",

            // 控制功能
            "控制", "调节", "调压", "调流", "调温", "调速",
            "启动", "停止", "开启", "关闭", "切换", "联锁",
            "自动", "手动", "远程", "就地", "紧急", "安全",

            // 检测参数
            "压力", "温度", "流量", "液位", "浓度", "密度",
            "振动", "转速", "电流", "电压", "功率", "频率",
            "pH值", "电导率", "浊度", "含氧量", "湿度",

            // 状态信号
            "报警", "故障", "异常", "正常", "运行", "停机",
            "高报", "低报", "高高报", "低低报", "预警",
            "指示", "显示", "监测", "检测", "测量", "采集",
            "开关", "位置", "状态", "反馈", "确认", "应答"
    };

    // 语义相似度权重矩阵
    private static final Map<String, String[]> SEMANTIC_GROUPS;
    
    static {
        Map<String, String[]> groups = new ConcurrentHashMap<>();
        groups.put("阀门类", new String[] { "阀门", "调压阀", "球阀", "蝶阀", "闸阀", "安全阀", "电磁阀" });
        groups.put("泵类", new String[] { "泵", "增压泵", "循环泵", "离心泵", "螺杆泵", "齿轮泵" });
        groups.put("过滤类", new String[] { "过滤器", "滤芯", "净化器", "分离器", "除尘器" });
        groups.put("加热类", new String[] { "加热器", "换热器", "冷却器", "蒸发器", "冷凝器" });
        groups.put("控制类", new String[] { "控制", "调节", "调压", "调流", "调温", "调速" });
        groups.put("检测类", new String[] { "检测", "监测", "测量", "采集", "指示", "显示" });
        groups.put("报警类", new String[] { "报警", "故障", "异常", "预警", "高报", "低报" });
        SEMANTIC_GROUPS = groups;
    }

    /**
     * 获取文本的嵌入向量
     * 
     * @param text 输入文本
     * @return 嵌入向量
     */
    public float[] getEmbedding(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new float[SEMANTIC_KEYWORDS.length];
        }

        // 检查缓存
        String key = text.toLowerCase().trim();
        if (embeddingCache.containsKey(key)) {
            return embeddingCache.get(key);
        }

        // 计算向量
        float[] embedding = computeEmbedding(text);
        embeddingCache.put(key, embedding);

        return embedding;
    }

    /**
     * 计算两个文本的余弦相似度
     * 
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度分数 (0-1)
     */
    public double calculateSimilarity(String text1, String text2) {
        // 1. 基础向量相似度
        float[] embedding1 = getEmbedding(text1);
        float[] embedding2 = getEmbedding(text2);
        double vectorSimilarity = cosineSimilarity(embedding1, embedding2);

        // 2. 语义组相似度增强
        double semanticBonus = calculateSemanticGroupSimilarity(text1, text2);

        // 3. 字符串相似度补充
        double stringBonus = calculateStringSimilarity(text1, text2);

        // 4. 综合相似度计算
        double finalSimilarity = vectorSimilarity * 0.6 + semanticBonus * 0.3 + stringBonus * 0.1;

        return Math.min(1.0, finalSimilarity);
    }

    /**
     * 批量计算相似度
     * 
     * @param queryText  查询文本
     * @param candidates 候选文本列表
     * @return 相似度分数数组
     */
    public double[] batchSimilarity(String queryText, List<String> candidates) {
        float[] queryEmbedding = getEmbedding(queryText);
        double[] similarities = new double[candidates.size()];

        for (int i = 0; i < candidates.size(); i++) {
            float[] candidateEmbedding = getEmbedding(candidates.get(i));
            similarities[i] = cosineSimilarity(queryEmbedding, candidateEmbedding);
        }

        return similarities;
    }

    /**
     * 计算嵌入向量（增强版本）
     * 
     * @param text 输入文本
     * @return 向量
     */
    private float[] computeEmbedding(String text) {
        float[] embedding = new float[SEMANTIC_KEYWORDS.length];
        String lowerText = text.toLowerCase();

        // 1. 基于关键词匹配的向量化
        for (int i = 0; i < SEMANTIC_KEYWORDS.length; i++) {
            String keyword = SEMANTIC_KEYWORDS[i];

            // 精确匹配
            if (lowerText.contains(keyword)) {
                embedding[i] = 1.0f;
            }

            // 部分匹配（词根）
            if (keyword.length() > 2) {
                String root = keyword.substring(0, keyword.length() - 1);
                if (lowerText.contains(root)) {
                    embedding[i] = Math.max(embedding[i], 0.7f);
                }
            }

            // 同义词匹配
            float synonymScore = getSynonymScore(lowerText, keyword);
            embedding[i] = Math.max(embedding[i], synonymScore);
        }

        // 2. 语义增强：基于上下文的权重调整
        embedding = enhanceSemanticWeights(embedding, lowerText);

        // 3. 归一化
        return normalizeVector(embedding);
    }

    /**
     * 语义增强：基于上下文调整权重
     * 
     * @param embedding 原始向量
     * @param text      文本
     * @return 增强后的向量
     */
    private float[] enhanceSemanticWeights(float[] embedding, String text) {
        float[] enhanced = embedding.clone();

        // 设备类型上下文增强
        if (text.contains("调压") || text.contains("控制")) {
            enhanceKeyword(enhanced, "阀门", 0.3f);
            enhanceKeyword(enhanced, "控制", 0.3f);
        }

        if (text.contains("增压") || text.contains("循环")) {
            enhanceKeyword(enhanced, "泵", 0.3f);
        }

        if (text.contains("过滤") || text.contains("净化")) {
            enhanceKeyword(enhanced, "过滤", 0.3f);
        }

        if (text.contains("加热") || text.contains("升温")) {
            enhanceKeyword(enhanced, "加热", 0.3f);
        }

        if (text.contains("报警") || text.contains("故障") || text.contains("警告")) {
            enhanceKeyword(enhanced, "报警", 0.3f);
            enhanceKeyword(enhanced, "故障", 0.3f);
        }

        if (text.contains("检测") || text.contains("监测") || text.contains("测量")) {
            enhanceKeyword(enhanced, "检测", 0.3f);
            enhanceKeyword(enhanced, "监测", 0.3f);
        }

        return enhanced;
    }

    /**
     * 增强特定关键词的权重
     * 
     * @param embedding 向量
     * @param keyword   关键词
     * @param boost     增强值
     */
    private void enhanceKeyword(float[] embedding, String keyword, float boost) {
        for (int i = 0; i < SEMANTIC_KEYWORDS.length; i++) {
            if (SEMANTIC_KEYWORDS[i].equals(keyword)) {
                embedding[i] = Math.min(1.0f, embedding[i] + boost);
                break;
            }
        }
    }

    /**
     * 获取同义词匹配分数
     * 
     * @param text    文本
     * @param keyword 关键词
     * @return 匹配分数
     */
    private float getSynonymScore(String text, String keyword) {
        switch (keyword) {
            case "阀门":
                if (text.contains("阀") || text.contains("valve"))
                    return 0.9f;
                break;
            case "压力":
                if (text.contains("压") || text.contains("pressure") || text.contains("pt"))
                    return 0.9f;
                break;
            case "温度":
                if (text.contains("温") || text.contains("temp") || text.contains("tt"))
                    return 0.9f;
                break;
            case "流量":
                if (text.contains("流") || text.contains("flow") || text.contains("ft"))
                    return 0.9f;
                break;
            case "液位":
                if (text.contains("液") || text.contains("位") || text.contains("level") || text.contains("lt"))
                    return 0.9f;
                break;
            case "控制":
                if (text.contains("控") || text.contains("制") || text.contains("control") || text.contains("xc"))
                    return 0.9f;
                break;
            case "检测":
                if (text.contains("检") || text.contains("测") || text.contains("detect") || text.contains("monitor"))
                    return 0.9f;
                break;
            case "报警":
                if (text.contains("报") || text.contains("警") || text.contains("alarm") || text.contains("ua"))
                    return 0.9f;
                break;
            case "状态":
                if (text.contains("状") || text.contains("态") || text.contains("status") || text.contains("zs"))
                    return 0.9f;
                break;
            case "指示":
                if (text.contains("指") || text.contains("示") || text.contains("indicate"))
                    return 0.9f;
                break;
        }
        return 0.0f;
    }

    /**
     * 向量归一化
     * 
     * @param vector 原始向量
     * @return 归一化向量
     */
    private float[] normalizeVector(float[] vector) {
        double norm = 0.0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);

        if (norm == 0.0) {
            return vector;
        }

        float[] normalized = new float[vector.length];
        for (int i = 0; i < vector.length; i++) {
            normalized[i] = (float) (vector[i] / norm);
        }

        return normalized;
    }

    /**
     * 计算余弦相似度
     * 
     * @param vec1 向量1
     * @param vec2 向量2
     * @return 相似度
     */
    private double cosineSimilarity(float[] vec1, float[] vec2) {
        if (vec1.length != vec2.length) {
            return 0.0;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vec1.length; i++) {
            dotProduct += vec1[i] * vec2[i];
            norm1 += vec1[i] * vec1[i];
            norm2 += vec2[i] * vec2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        embeddingCache.clear();
        logger.info("嵌入向量缓存已清理");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return embeddingCache.size();
    }

    /**
     * 计算语义组相似度
     * 
     * @param text1 文本1
     * @param text2 文本2
     * @return 语义组相似度
     */
    private double calculateSemanticGroupSimilarity(String text1, String text2) {
        String lowerText1 = text1.toLowerCase();
        String lowerText2 = text2.toLowerCase();

        double maxSimilarity = 0.0;

        // 检查是否属于同一语义组
        for (Map.Entry<String, String[]> entry : SEMANTIC_GROUPS.entrySet()) {
            String[] keywords = entry.getValue();
            boolean text1InGroup = false;
            boolean text2InGroup = false;

            for (String keyword : keywords) {
                if (lowerText1.contains(keyword)) {
                    text1InGroup = true;
                }
                if (lowerText2.contains(keyword)) {
                    text2InGroup = true;
                }
            }

            if (text1InGroup && text2InGroup) {
                maxSimilarity = Math.max(maxSimilarity, 0.8); // 同组高相似度
            } else if (text1InGroup || text2InGroup) {
                maxSimilarity = Math.max(maxSimilarity, 0.2); // 单边匹配低相似度
            }
        }

        return maxSimilarity;
    }

    /**
     * 计算字符串相似度
     * 
     * @param text1 文本1
     * @param text2 文本2
     * @return 字符串相似度
     */
    private double calculateStringSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0.0;
        }

        String lower1 = text1.toLowerCase();
        String lower2 = text2.toLowerCase();

        // 1. 完全匹配
        if (lower1.equals(lower2)) {
            return 1.0;
        }

        // 2. 包含关系
        if (lower1.contains(lower2) || lower2.contains(lower1)) {
            return 0.7;
        }

        // 3. 编辑距离相似度
        int editDistance = calculateEditDistance(lower1, lower2);
        int maxLength = Math.max(lower1.length(), lower2.length());

        if (maxLength == 0) {
            return 1.0;
        }

        return 1.0 - (double) editDistance / maxLength;
    }

    /**
     * 计算编辑距离（Levenshtein距离）
     * 
     * @param s1 字符串1
     * @param s2 字符串2
     * @return 编辑距离
     */
    private int calculateEditDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();

        int[][] dp = new int[m + 1][n + 1];

        // 初始化
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }

        // 动态规划计算
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }

        return dp[m][n];
    }
}
