package com.example.demo.service;

import com.example.demo.model.Device;
import com.example.demo.model.IoPoint;
import com.example.demo.parser.PdfDocumentParser;
import com.example.demo.parser.WordDocumentParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件解析服务
 * 统一处理不同类型文件的解析
 */
@Service
public class FileParsingService {

    private static final Logger logger = LoggerFactory.getLogger(FileParsingService.class);

    private final WordDocumentParser wordParser;
    private final PdfDocumentParser pdfParser;
    private final DeviceRecognitionService deviceRecognitionService;

    /**
     * 构造函数
     */
    public FileParsingService() {
        this.wordParser = new WordDocumentParser();
        this.pdfParser = new PdfDocumentParser();
        this.deviceRecognitionService = new DeviceRecognitionService();
    }

    /**
     * 解析文件并提取设备信息
     * 
     * @param filePath 文件路径
     * @return 解析结果
     */
    public ParseResult parseFile(String filePath) {
        logger.info("开始解析文件: {}", filePath);
        
        ParseResult result = new ParseResult();
        result.setFilePath(filePath);
        result.setFileType(detectFileType(filePath));
        
        try {
            // 根据文件类型选择解析器
            List<IoPoint> ioPoints = parseByFileType(filePath, result.getFileType());
            result.setIoPoints(ioPoints);
            result.setSuccess(true);
            
            // 使用设备识别服务进行设备分组
            List<Device> devices = deviceRecognitionService.recognizeDevices(ioPoints);
            result.setDevices(devices);
            
            logger.info("文件解析成功，提取{}个IO点位，识别{}个设备", 
                       ioPoints.size(), devices.size());
                       
        } catch (Exception e) {
            logger.error("文件解析失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量解析文件
     * 
     * @param filePaths 文件路径列表
     * @return 解析结果列表
     */
    public List<ParseResult> parseFiles(List<String> filePaths) {
        List<ParseResult> results = new ArrayList<>();
        
        for (String filePath : filePaths) {
            ParseResult result = parseFile(filePath);
            results.add(result);
        }
        
        return results;
    }

    /**
     * 检测文件类型
     * 
     * @param filePath 文件路径
     * @return 文件类型
     */
    private String detectFileType(String filePath) {
        String lowerPath = filePath.toLowerCase();
        
        if (lowerPath.endsWith(".docx") || lowerPath.endsWith(".doc")) {
            return "WORD";
        } else if (lowerPath.endsWith(".pdf")) {
            return "PDF";
        } else if (lowerPath.endsWith(".xlsx") || lowerPath.endsWith(".xls")) {
            return "EXCEL";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 根据文件类型选择解析器
     * 
     * @param filePath 文件路径
     * @param fileType 文件类型
     * @return IO点位列表
     * @throws IOException 解析异常
     */
    private List<IoPoint> parseByFileType(String filePath, String fileType) throws IOException {
        switch (fileType) {
            case "WORD":
                return wordParser.parseDocument(filePath);
            case "PDF":
                return pdfParser.parseDocument(filePath);
            case "EXCEL":
                // TODO: 实现Excel解析器
                logger.warn("Excel解析功能尚未实现");
                return new ArrayList<>();
            default:
                throw new IOException("不支持的文件类型: " + fileType);
        }
    }

    /**
     * 获取支持的文件类型
     * 
     * @return 支持的文件扩展名列表
     */
    public List<String> getSupportedFileTypes() {
        List<String> supportedTypes = new ArrayList<>();
        supportedTypes.add(".docx");
        supportedTypes.add(".doc");
        supportedTypes.add(".pdf");
        supportedTypes.add(".xlsx");
        supportedTypes.add(".xls");
        return supportedTypes;
    }

    /**
     * 解析结果类
     */
    public static class ParseResult {
        private String filePath;
        private String fileType;
        private boolean success;
        private String errorMessage;
        private List<IoPoint> ioPoints;
        private List<Device> devices;

        // Getters and Setters
        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public List<IoPoint> getIoPoints() {
            return ioPoints;
        }

        public void setIoPoints(List<IoPoint> ioPoints) {
            this.ioPoints = ioPoints;
        }

        public List<Device> getDevices() {
            return devices;
        }

        public void setDevices(List<Device> devices) {
            this.devices = devices;
        }

        @Override
        public String toString() {
            return "ParseResult{" +
                    "filePath='" + filePath + '\'' +
                    ", fileType='" + fileType + '\'' +
                    ", success=" + success +
                    ", ioPointsCount=" + (ioPoints != null ? ioPoints.size() : 0) +
                    ", devicesCount=" + (devices != null ? devices.size() : 0) +
                    '}';
        }
    }
}