package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * I/O点位数据模型
 * 对应I/O表中的一行记录
 */
public class IoPoint {

    @JsonProperty("tag")
    private String tag; // 仪表位号，如 XZ-4101

    @JsonProperty("desc")
    private String desc; // 描述，如 调压系统A监控调压阀阀位指示

    @JsonProperty("signalType")
    private String signalType; // 信号类型：AI/DI/AO/DO/RS485

    @JsonProperty("unit")
    private String unit; // 单位

    @JsonProperty("range")
    private String range; // 量程

    @JsonProperty("rawRowId")
    private String rawRowId; // 原表序号

    @JsonProperty("deviceId")
    private String deviceId; // 所属设备ID

    @JsonProperty("bindMethod")
    private String bindMethod; // 绑定方法：RULE/EMBED/MANUAL

    @JsonProperty("sourceRow")
    private int sourceRow; // 源文档行号

    // 构造函数
    public IoPoint() {
    }

    public IoPoint(String tag, String desc) {
        this.tag = tag;
        this.desc = desc;
    }

    // Getter和Setter方法
    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSignalType() {
        return signalType;
    }

    public void setSignalType(String signalType) {
        this.signalType = signalType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public String getRawRowId() {
        return rawRowId;
    }

    public void setRawRowId(String rawRowId) {
        this.rawRowId = rawRowId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getBindMethod() {
        return bindMethod;
    }

    public void setBindMethod(String bindMethod) {
        this.bindMethod = bindMethod;
    }

    public int getSourceRow() {
        return sourceRow;
    }

    public void setSourceRow(int sourceRow) {
        this.sourceRow = sourceRow;
    }

    @Override
    public String toString() {
        return "IoPoint{" +
                "tag='" + tag + '\'' +
                ", desc='" + desc + '\'' +
                ", signalType='" + signalType + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", bindMethod='" + bindMethod + '\'' +
                '}';
    }
}
