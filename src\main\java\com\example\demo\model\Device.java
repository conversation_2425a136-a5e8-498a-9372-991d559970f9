package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备数据模型
 * 包含传感器、执行器、状态信号三类点位
 */
public class Device {

    @JsonProperty("deviceId")
    private String deviceId; // 设备ID，如 4101

    @JsonProperty("category")
    private String category; // 设备类别，如 阀门

    @JsonProperty("aliases")
    private List<String> aliases = new ArrayList<>(); // 别名列表

    @JsonProperty("sensors")
    private List<IoPoint> sensors = new ArrayList<>(); // 传感器点位

    @JsonProperty("actuators")
    private List<IoPoint> actuators = new ArrayList<>(); // 执行器点位

    @JsonProperty("statusSignals")
    private List<IoPoint> statusSignals = new ArrayList<>(); // 状态信号点位

    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    // 构造函数
    public Device() {
        this.createdAt = LocalDateTime.now();
    }

    public Device(String deviceId) {
        this.deviceId = deviceId;
        this.createdAt = LocalDateTime.now();
    }

    public Device(String deviceId, String category) {
        this.deviceId = deviceId;
        this.category = category;
        this.createdAt = LocalDateTime.now();
    }

    // Getter和Setter方法
    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getAliases() {
        return aliases;
    }

    public void setAliases(List<String> aliases) {
        this.aliases = aliases;
    }

    public void addAlias(String alias) {
        if (!this.aliases.contains(alias)) {
            this.aliases.add(alias);
        }
    }

    public List<IoPoint> getSensors() {
        return sensors;
    }

    public void setSensors(List<IoPoint> sensors) {
        this.sensors = sensors;
    }

    public void addSensor(IoPoint sensor) {
        this.sensors.add(sensor);
        sensor.setDeviceId(this.deviceId);
    }

    public List<IoPoint> getActuators() {
        return actuators;
    }

    public void setActuators(List<IoPoint> actuators) {
        this.actuators = actuators;
    }

    public void addActuator(IoPoint actuator) {
        this.actuators.add(actuator);
        actuator.setDeviceId(this.deviceId);
    }

    public List<IoPoint> getStatusSignals() {
        return statusSignals;
    }

    public void setStatusSignals(List<IoPoint> statusSignals) {
        this.statusSignals = statusSignals;
    }

    public void addStatusSignal(IoPoint statusSignal) {
        this.statusSignals.add(statusSignal);
        statusSignal.setDeviceId(this.deviceId);
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 获取所有点位的总数
     */
    public int getTotalPointCount() {
        return sensors.size() + actuators.size() + statusSignals.size();
    }

    /**
     * 获取所有点位
     */
    public List<IoPoint> getAllPoints() {
        List<IoPoint> allPoints = new ArrayList<>();
        allPoints.addAll(sensors);
        allPoints.addAll(actuators);
        allPoints.addAll(statusSignals);
        return allPoints;
    }

    @Override
    public String toString() {
        return "Device{" +
                "deviceId='" + deviceId + '\'' +
                ", category='" + category + '\'' +
                ", aliases=" + aliases +
                ", sensors=" + sensors.size() +
                ", actuators=" + actuators.size() +
                ", statusSignals=" + statusSignals.size() +
                '}';
    }
}
