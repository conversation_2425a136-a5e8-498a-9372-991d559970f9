package com.example.demo.parser;

import com.example.demo.service.EmbeddingService;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 语义化表头检测器
 * 使用语义相似度识别表格的列角色和表头位置
 */
public class SemanticHeaderDetector {

    private static final Logger logger = LoggerFactory.getLogger(SemanticHeaderDetector.class);

    private final EmbeddingService embeddingService;

    // 相似度阈值（进一步降低以提高检测成功率）
    private static final double HIGH_CONFIDENCE_THRESHOLD = 0.65; // 高置信度
    private static final double LOW_CONFIDENCE_THRESHOLD = 0.45; // 低置信度
    private static final double MIN_ROW_SCORE = 0.20; // 最低行得分（降低）
    private static final int MIN_CORE_COLUMNS = 2; // 最少核心列数

    public SemanticHeaderDetector(EmbeddingService embeddingService) {
        this.embeddingService = embeddingService;
    }

    /**
     * 检测表格的表头和列映射
     * 
     * @param table Word表格
     * @return 列角色映射，如果检测失败返回null
     */
    public Map<ColumnRole, Integer> detectHeader(XWPFTable table) {
        List<XWPFTableRow> rows = table.getRows();
        if (rows.isEmpty()) {
            return null;
        }

        Map<ColumnRole, Integer> bestMapping = null;
        double bestScore = 0.0;
        int bestRowIndex = -1;

        // 扫描前10行寻找最佳表头
        int maxScan = Math.min(10, rows.size());
        for (int rowIndex = 0; rowIndex < maxScan; rowIndex++) {
            XWPFTableRow row = rows.get(rowIndex);

            // 跳过明显的非表头行
            if (isObviousNonHeaderRow(row)) {
                // logger.debug("跳过非表头行: {}", rowIndex + 1);
                continue;
            }

            // 计算该行的列角色映射和得分
            HeaderDetectionResult result = analyzeRowAsHeader(row, rowIndex);
            if (result != null && result.score > bestScore && result.score >= MIN_ROW_SCORE) {
                bestScore = result.score;
                bestMapping = result.columnMapping;
                bestRowIndex = rowIndex;
            }
        }

        if (bestMapping != null) {
            // logger.info("语义表头检测成功: 行{}, 得分{:.3f}, 识别列数{}",
            // bestRowIndex + 1, bestScore, bestMapping.size());
            // logColumnMapping(bestMapping);
        } else {
            logger.warn("语义表头检测失败，未找到符合条件的表头行");
            logTableStructure(table);
        }

        return bestMapping;
    }

    /**
     * 分析一行是否为表头
     * 
     * @param row      表格行
     * @param rowIndex 行索引
     * @return 检测结果
     */
    private HeaderDetectionResult analyzeRowAsHeader(XWPFTableRow row, int rowIndex) {
        List<XWPFTableCell> cells = row.getTableCells();
        if (cells.size() < 4) { // 至少需要4列
            return null;
        }

        Map<ColumnRole, ColumnMatch> roleMatches = new EnumMap<>(ColumnRole.class);

        // 为每个单元格计算与各列角色的相似度
        for (int colIndex = 0; colIndex < cells.size(); colIndex++) {
            String cellText = getCellText(cells.get(colIndex));
            if (cellText.trim().isEmpty()) {
                continue;
            }

            // 计算与每个列角色的最佳匹配
            for (ColumnRole role : ColumnRole.getAllRoles()) {
                double similarity = calculateRoleSimilarity(cellText, role);

                // 添加调试信息
                if (similarity > 0.3) {
                    // logger.debug("列[{}]\"{}\" vs {}角色: 相似度{}",
                    // colIndex, cellText, role.getDisplayName(), String.format("%.3f",
                    // similarity));
                }

                if (similarity >= LOW_CONFIDENCE_THRESHOLD) {
                    ColumnMatch existingMatch = roleMatches.get(role);
                    if (existingMatch == null || similarity > existingMatch.similarity) {
                        roleMatches.put(role, new ColumnMatch(colIndex, similarity));
                    }
                }
            }
        }

        // 计算行得分
        HeaderDetectionResult result = calculateRowScore(roleMatches, rowIndex);
        if (result != null) {
            // logger.debug("行{}得分: {}, 匹配列数: {}", rowIndex + 1,
            // String.format("%.3f", result.score), roleMatches.size());
        }
        return result;
    }

    /**
     * 计算文本与列角色的相似度
     * 
     * @param cellText 单元格文本
     * @param role     列角色
     * @return 相似度分数
     */
    private double calculateRoleSimilarity(String cellText, ColumnRole role) {
        double maxSimilarity = 0.0;

        for (String prototype : role.getPrototypes()) {
            double similarity = embeddingService.calculateSimilarity(cellText, prototype);
            maxSimilarity = Math.max(maxSimilarity, similarity);
        }

        return maxSimilarity;
    }

    /**
     * 计算行得分
     * 
     * @param roleMatches 角色匹配结果
     * @param rowIndex    行索引
     * @return 检测结果
     */
    private HeaderDetectionResult calculateRowScore(Map<ColumnRole, ColumnMatch> roleMatches, int rowIndex) {
        if (roleMatches.isEmpty()) {
            return null;
        }

        // 检查核心列覆盖率
        long coreRoleCount = roleMatches.keySet().stream()
                .filter(ColumnRole::isCore)
                .count();

        if (coreRoleCount < MIN_CORE_COLUMNS) {
            return null; // 核心列不足
        }

        // 计算平均置信度
        double totalSimilarity = roleMatches.values().stream()
                .mapToDouble(match -> match.similarity)
                .sum();
        double avgSimilarity = totalSimilarity / roleMatches.size();

        // 计算覆盖率
        double coverageRate = (double) roleMatches.size() / ColumnRole.getAllRoles().size();

        // 综合得分：覆盖率 * 平均相似度
        double score = coverageRate * avgSimilarity;

        // 转换为列映射
        Map<ColumnRole, Integer> columnMapping = new EnumMap<>(ColumnRole.class);
        roleMatches.forEach((role, match) -> columnMapping.put(role, match.columnIndex));

        return new HeaderDetectionResult(columnMapping, score, rowIndex);
    }

    /**
     * 判断是否为明显的非表头行
     * 
     * @param row 表格行
     * @return 是否为非表头行
     */
    private boolean isObviousNonHeaderRow(XWPFTableRow row) {
        List<XWPFTableCell> cells = row.getTableCells();
        if (cells.isEmpty()) {
            return true;
        }

        int textCellCount = 0;
        int numberCellCount = 0;

        for (XWPFTableCell cell : cells) {
            String cellText = getCellText(cell).trim();
            if (cellText.isEmpty()) {
                continue;
            }

            if (cellText.matches("\\d+(\\.\\d+)?")) {
                numberCellCount++;
            } else {
                textCellCount++;
            }
        }

        // 如果超过70%是纯数字，可能不是表头
        return numberCellCount > 0 && (double) numberCellCount / (textCellCount + numberCellCount) > 0.7;
    }

    /**
     * 获取单元格文本
     * 
     * @param cell 单元格
     * @return 文本内容
     */
    private String getCellText(XWPFTableCell cell) {
        return cell.getText().trim();
    }

    /**
     * 记录列映射信息
     * 
     * @param columnMapping 列映射
     */
    private void logColumnMapping(Map<ColumnRole, Integer> columnMapping) {
        StringBuilder sb = new StringBuilder("检测到的列映射: ");
        columnMapping.forEach((role, index) -> sb.append(role.getDisplayName()).append("=").append(index).append(" "));
        // logger.info(sb.toString());
    }

    /**
     * 记录表格结构用于调试
     * 
     * @param table 表格
     */
    private void logTableStructure(XWPFTable table) {
        logger.debug("表格结构调试信息:");
        List<XWPFTableRow> rows = table.getRows();
        for (int i = 0; i < Math.min(5, rows.size()); i++) {
            List<XWPFTableCell> cells = rows.get(i).getTableCells();
            StringBuilder sb = new StringBuilder("行" + (i + 1) + ": ");
            for (int j = 0; j < cells.size(); j++) {
                sb.append("[").append(j).append("]").append(getCellText(cells.get(j))).append(" ");
            }
            logger.debug(sb.toString());
        }
    }

    /**
     * 列匹配结果
     */
    private static class ColumnMatch {
        final int columnIndex;
        final double similarity;

        ColumnMatch(int columnIndex, double similarity) {
            this.columnIndex = columnIndex;
            this.similarity = similarity;
        }
    }

    /**
     * 表头检测结果
     */
    private static class HeaderDetectionResult {
        final Map<ColumnRole, Integer> columnMapping;
        final double score;
        final int rowIndex;

        HeaderDetectionResult(Map<ColumnRole, Integer> columnMapping, double score, int rowIndex) {
            this.columnMapping = columnMapping;
            this.score = score;
            this.rowIndex = rowIndex;
        }
    }
}
