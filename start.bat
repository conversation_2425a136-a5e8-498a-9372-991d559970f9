@echo off
echo 正在启动设计院资料识别系统...
echo.

REM 检查Java版本
java -version
echo.

REM 设置classpath（包含所有依赖jar）
set CLASSPATH=lib\*;target\classes;src\main\resources

REM 编译Java源代码
echo 正在编译Java源代码...
if not exist "target\classes" mkdir "target\classes"

REM 编译所有Java文件
javac -cp "lib\*" -d "target\classes" -encoding UTF-8 ^
    "src\main\java\com\example\demo\*.java" ^
    "src\main\java\com\example\demo\model\*.java" ^
    "src\main\java\com\example\demo\service\*.java" ^
    "src\main\java\com\example\demo\parser\*.java" ^
    "src\main\java\com\example\demo\controller\*.java"

if %errorlevel% neq 0 (
    echo 编译失败！请检查代码是否有语法错误。
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 复制资源文件
echo 复制资源文件...
if not exist "target\classes\static" mkdir "target\classes\static"
copy "src\main\resources\static\*.*" "target\classes\static\"
copy "src\main\resources\*.*" "target\classes\"

echo.
echo 正在启动Spring Boot应用...
echo 访问地址: http://localhost:8080
echo 按Ctrl+C停止服务
echo.

REM 启动应用
java -cp "lib\*;target\classes" com.example.demo.Application

pause