@echo off
chcp 65001 >nul
echo.
echo 🚀 使用JDK 1.8启动设计院资料识别系统
echo.

REM 设置JDK 1.8路径
set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM 验证Java版本
echo 🔍 验证Java版本...
"%JAVA_HOME%\bin\java.exe" -version
if %errorLevel% neq 0 (
    echo ❌ JDK 1.8未正确安装或配置！
    echo 请检查安装路径：%JAVA_HOME%
    pause
    exit /b 1
)

echo ✅ JDK 1.8验证成功！
echo.

REM 创建编译目录
if not exist "target\classes" mkdir "target\classes"

REM 设置classpath
set "CLASSPATH=lib\*;target\classes;src\main\resources"

echo 🔨 编译Java源代码...
echo 使用JDK 1.8编译器：%JAVA_HOME%\bin\javac.exe
echo.

REM 编译所有Java文件
"%JAVA_HOME%\bin\javac.exe" -cp "lib\*" -d "target\classes" -encoding UTF-8 ^
    "src\main\java\com\example\demo\model\*.java" ^
    "src\main\java\com\example\demo\service\*.java" ^
    "src\main\java\com\example\demo\parser\*.java" ^
    "src\main\java\com\example\demo\controller\*.java" ^
    "src\main\java\com\example\demo\*.java"

if %errorLevel% neq 0 (
    echo ❌ 编译失败！请检查代码语法错误。
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

REM 复制资源文件
echo 📁 复制资源文件...
if not exist "target\classes\static" mkdir "target\classes\static"
if exist "src\main\resources\static\*.*" copy "src\main\resources\static\*.*" "target\classes\static\" >nul
if exist "src\main\resources\*.*" copy "src\main\resources\*.*" "target\classes\" >nul 2>nul

echo ✅ 资源文件复制完成！
echo.

REM 启动Web服务器
echo 🌐 启动Web服务器...
echo 访问地址: http://localhost:8888
echo 按Ctrl+C停止服务
echo.

REM 使用JDK 1.8启动
"%JAVA_HOME%\bin\java.exe" -cp "lib\*;target\classes" WebServer

pause