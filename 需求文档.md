# 设计院I/O 表解析与设备‑点位绑定系统

**软件需求规格说明书（SRS）**

------

## 1 项目背景

设计院交付的 I/O 汇总表，需要自动完成 **按设备分类** 与 **点位绑定**，并向 SCADA/BIM/移动运维等系统提供查询服务。当前做法依赖人工处理 Excel，效率低且易出错。

## 2 编写目的

本文档面向产品经理、架构师、开发与测试人员，明确系统的**功能需求、性能指标、约束条件与验收标准**，为后续设计、编码和测试提供依据。

## 3 术语与缩写

| 缩写         | 说明                                                         |
| ------------ | ------------------------------------------------------------ |
| **I/O 表**   | 设计院交付的《I/O 汇总表》Word/Excel/PDF 文件                |
| **设备**     | 以数字为核心编号的物理对象，如 **4101阀门**                  |
| **点位**     | I/O 表中的一行信号（含位号、描述、量程等）                   |
| **嵌入模型** | TinyBERT / text2vec‑base‑chinese 等产生句向量的轻量 NLP 模型 |
| **BPCS/ESD** | 控制系统分区标识；仅作分类字段存储                           |

## 4 总体目标与范围

1. **离线解析**：在本地 Java 服务中解析 .docx / .xlsx /PDF，抽取行级点位。
2. **设备识别**：基于编号 + 规则 + 语义模型，生成唯一设备实体并归并别名。
3. **点位绑定**：精度 ≥ 95 %，召回 ≥ 98 %；未绑定项须标记待确认。
4. **查询接口**：REST API / CLI 返回「设备→点位」及「点位详情」。
5. **持续学习**：支持增量规则文件与人工校正集，模型可热加载。
6. **可视化验证**（可选）：Web 前端树形控件高亮异常绑定。

> **超出范围**：与 SCADA 实时数据库对接、BIM 三维定位、权限体系。

## 5 系统环境

| 分类     | 约束                                                |
| -------- | --------------------------------------------------- |
| 运行环境 | JDK 17+；4 C 8 G RAM；无外网亦可运行                |
| 依赖     | Apache POI、ONNX‑Runtime、Caffeine、Spring Boot 3.x |
| 部署     | Windows 10/11 或 CentOS 7+；Docker 可选             |
| 安全     | 全程离线；无第三方云调用；文档仅本地落盘            |

## 6 功能需求

### 6.1 文档导入 F‑01

| 需求编号 | F‑01                                                         |
| -------- | ------------------------------------------------------------ |
| 描述     | 支持上传/监视 .docx /.xlsx/PDF；解析多表头、合并单元格；输出 JSON 行集 |
| 触发     | 命令行 `import <file>` 或 REST `POST /import`                |
| 约束     | 单文件 ≤ 5 MB；解析时间 ≤ 1 s/1000 行                        |
| 错误处理 | 不合规行写入 `error_rows.csv` 并记录日志                     |

### 6.2 设备识别 F‑02

- **规则层**
  - 正则 `([A-Z]+)-(\\d{4})(?:-(\\d+))?` 提取核心编号
  - 同编号聚合成一个 `Device` 实例
- **语义层**
  - 对剩余未归类行，计算描述向量与已归类描述向量余弦相似度 (`θ ≥ 0.75`) 归属
  - 若仍未归属，标记 **pending**，等待人工确认
- **输出**：`device.json`（含字段 `deviceId`,`category`,`aliases`）

### 6.3 点位归属 F‑03

| 核心逻辑 | 先规则（前缀→传感/控制/状态），后向量补全，最后人工确认 |
 | 结果 | 每个 `Device` 下生成 `sensors[]`,`actuators[]`,`statusSignals[]` |
 | 可追溯 | 每条 `IoPoint` 记录字段 `sourceRow`,`bindMethod`（RULE/EMBED/MANUAL） |

### 6.4 人工校正 F‑04

- CLI `device edit <deviceId>` / Web UI 拖放
- 结果写入 `rules.yaml`（正则黑白名单）或 `feedback.csv`（训练集）

### 6.5 查询接口 F‑05

| Endpoint           | 说明                            |
| ------------------ | ------------------------------- |
| `GET /devices`     | 列出全部设备（分页 / 模糊搜索） |
| `GET /device/{id}` | 返回设备信息及三类点位          |
| `GET /point/{tag}` | 返回点位详情（含所属设备）      |
| `GET /stats`       | 返回已绑定、待确认、错误行统计  |

### 6.6 模型与配置热加载 F‑06

重载触发：文件变更监听或 `POST /reload`；重载耗时 ≤ 1 s；不中断服务。

## 7 数据需求

| 实体     | 主键       | 关键字段                                            |
| -------- | ---------- | --------------------------------------------------- |
| Device   | `deviceId` | `category`,`aliases`,`createdAt`                    |
| IoPoint  | `tag`      | `desc`,`signalType`,`signalRange`,`unit`,`deviceId` |
| Rule     | `pattern`  | `targetCategory`,`priority`                         |
| Feedback | `tag`      | `deviceId`,`isCorrect`                              |

数据库可用 SQLite（嵌入式）或 MySQL；日后可迁移至 PostgreSQL。

## 8 非功能需求

| 类别     | 指标                                        |
| -------- | ------------------------------------------- |
| 性能     | 300 行 I/O 表 → 结构化 & 绑定 ≤ 3 s         |
| 准确性   | 绑定精度 ≥ 95 %，召回 ≥ 98 %                |
| 扩展性   | 新模型文件热插拔；规则文件 YAML 即改即生效  |
| 可维护性 | 代码分层；单元测试覆盖率 ≥ 70 %             |
| 可靠性   | 解析/绑定失败不影响已入库数据；提供每日备份 |
| 安全     | 全离线；不写入明文密码；日志脱敏文件路径    |

## 9 接口协议（示例）

```
GET /device/4101
HTTP/1.1 200 OK
{
  "deviceId": "4101",
  "category": "阀门",
  "aliases": ["XZ-4101", "XC-4101", "UA-4101"],
  "sensors": [
    {"tag":"PT-4102","desc":"后压力检测", ...}
  ],
  "actuators":[
    {"tag":"XC-4101", "desc":"阀位控制", ...}
  ],
  "statusSignals":[
    {"tag":"UA-4101","desc":"综合故障", ...}
  ]
}
```

## 10 里程碑与交付

| 阶段 | 任务               | 产出               | 计划工期  |
| ---- | ------------------ | ------------------ | --------- |
| M1   | 环境搭建、POI 解析 | MVP 导入模块       | T0 + 1 周 |
| M2   | 规则绑定、API 框架 | 可查询绑定结果     | + 2 周    |
| M3   | 嵌入模型接入       | 精度达标（≥ 90 %） | + 1 周    |
| M4   | 人工校正 UI        | 完整闭环           | + 1 周    |
| M5   | 压测与验收         | SRS 指标全部通过   | + 1 周    |

## 11 验收标准

1. **功能**：对示例 I/O 表全量解析，误绑定 ≤ 5 条；未绑定 ≤ 5 条。
2. **性能**：笔记本 4 C 8 G 下 300 行耗时 < 3 s；内存峰值 < 1 GB。
3. **接口**：Swagger / OpenAPI 文档齐全，通过 Postman 用例 100 % 通过。
4. **文档**：交付《部署手册》《运维手册》《用户手册》各 1 份。

