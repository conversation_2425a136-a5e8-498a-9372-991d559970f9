<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel下载功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 200px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            display: none;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Excel下载功能演示</h1>
        
        <div class="info">
            <h3>统一Excel表格包含以下内容：</h3>
            <ul>
                <li><strong>统计总览</strong> - 文件总数、IO点位总数、设备总数</li>
                <li><strong>统一IO点位表格</strong> - 所有文件的IO点位合并，包含源文件、位号、描述、信号类型、单位、量程、所属设备等</li>
                <li><strong>统一设备表格</strong> - 所有文件的设备合并，包含设备ID、名称、类型、关联点位数等</li>
                <li><strong>文件解析状态</strong> - 每个文件的解析结果和状态</li>
            </ul>
        </div>
        
        <button class="download-btn" onclick="downloadUnifiedExcel()">
            📋 下载统一Excel表格
        </button>
        
        <button class="download-btn" onclick="testDataPreview()">
            👀 预览数据结构
        </button>
        
        <div id="successMsg" class="success"></div>
        <div id="errorMsg" class="error"></div>
        
        <div id="dataPreview" style="display: none; text-align: left; margin-top: 20px;">
            <h3>数据预览：</h3>
            <pre id="dataContent" style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;"></pre>
        </div>
    </div>

    <script>
        function showSuccess(message) {
            const successDiv = document.getElementById('successMsg');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        // 下载统一的Excel表格
        function downloadUnifiedExcel() {
            showSuccess('正在获取数据...');
            
            // 获取批量解析数据
            fetch('http://localhost:8890/api/parsing/parse-test-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    generateUnifiedExcel(data.results);
                } else {
                    showError('获取数据失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络请求失败，请检查服务器连接');
            });
        }
        
        // 生成统一的Excel文件
        function generateUnifiedExcel(results) {
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // 标题信息
            csvContent += "设计院资料识别系统 - 统一数据表格\n";
            csvContent += "生成时间," + new Date().toLocaleString('zh-CN') + "\n\n";
            
            // 统计总览
            let totalFiles = Object.keys(results).length;
            let totalIoPoints = 0;
            let totalDevices = 0;
            
            Object.values(results).forEach(result => {
                if (result.success) {
                    totalIoPoints += result.ioPointsCount || 0;
                    totalDevices += result.devicesCount || 0;
                }
            });
            
            csvContent += "统计总览\n";
            csvContent += "文件总数," + totalFiles + "\n";
            csvContent += "IO点位总数," + totalIoPoints + "\n";
            csvContent += "设备总数," + totalDevices + "\n\n";
            
            // 统一的IO点位表格
            csvContent += "统一IO点位表格\n";
            csvContent += "序号,源文件,位号,描述,信号类型,单位,量程,所属设备,设备类型,源行号\n";
            
            let pointIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                if (result.success && result.ioPoints) {
                    // 创建设备映射表
                    const deviceMap = {};
                    if (result.devices) {
                        result.devices.forEach(device => {
                            deviceMap[device.deviceId] = device;
                        });
                    }
                    
                    result.ioPoints.forEach(point => {
                        const device = deviceMap[point.deviceId];
                        csvContent += [
                            pointIndex++,
                            fileName,
                            point.tag || '-',
                            point.desc || '-',
                            point.signalType || '-',
                            point.unit || '-',
                            point.range || '-',
                            device ? device.deviceName : (point.deviceId || '-'),
                            device ? device.deviceType : '-',
                            point.sourceRow || '-'
                        ].join(',') + '\n';
                    });
                }
            });
            
            csvContent += '\n';
            
            // 统一设备表格
            csvContent += "统一设备表格\n";
            csvContent += "序号,源文件,设备ID,设备名称,设备类型,关联IO点位数,创建时间\n";
            
            let deviceIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                if (result.success && result.devices) {
                    result.devices.forEach(device => {
                        // 计算关联的IO点位数
                        const relatedPoints = result.ioPoints ? 
                            result.ioPoints.filter(point => point.deviceId === device.deviceId).length : 0;
                        
                        csvContent += [
                            deviceIndex++,
                            fileName,
                            device.deviceId || '-',
                            device.deviceName || '-',
                            device.deviceType || '-',
                            relatedPoints,
                            device.createdAt || '-'
                        ].join(',') + '\n';
                    });
                }
            });
            
            csvContent += '\n';
            
            // 文件解析状态表格
            csvContent += "文件解析状态\n";
            csvContent += "序号,文件名,解析状态,文件类型,IO点位数,设备数,错误信息\n";
            
            let fileIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                csvContent += [
                    fileIndex++,
                    fileName,
                    result.success ? '成功' : '失败',
                    result.fileType || '-',
                    result.ioPointsCount || 0,
                    result.devicesCount || 0,
                    result.error || '-'
                ].join(',') + '\n';
            });
            
            // 添加UTF-8 BOM以确保Excel正确识别中文
            const BOM = '\uFEFF';
            const csvWithBOM = BOM + csvContent.substr(csvContent.indexOf(',') + 1);
            
            // 创建Blob对象确保正确的UTF-8编码
            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            
            // 创建下载链接
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "统一数据表格_" + new Date().toISOString().slice(0,19).replace(/:/g, '-') + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            URL.revokeObjectURL(url);
            
            showSuccess('统一Excel表格已开始下载！包含' + totalIoPoints + '个IO点位和' + totalDevices + '个设备');
        }
        
        // 预览数据结构
        function testDataPreview() {
            fetch('http://localhost:8890/api/parsing/parse-test-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('dataContent').textContent = JSON.stringify(data, null, 2);
                    document.getElementById('dataPreview').style.display = 'block';
                    showSuccess('数据预览已显示');
                } else {
                    showError('获取数据失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络请求失败，请检查服务器连接');
            });
        }
    </script>
</body>
</html>