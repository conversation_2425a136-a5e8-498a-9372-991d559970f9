@echo off
chcp 65001 >nul
echo.
echo 🚀 JDK 1.8 自动安装脚本
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限！
    echo 请右键点击此脚本并选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认
echo.

REM 检查是否已安装JDK 1.8
echo 🔍 检查当前Java版本...
java -version 2>nul
if %errorLevel% equ 0 (
    echo 当前系统已安装Java，版本信息：
    java -version
    echo.
    set /p choice="是否继续安装JDK 1.8？(y/n): "
    if /i not "%choice%"=="y" (
        echo 安装取消
        pause
        exit /b 0
    )
)

REM 设置下载参数
set JDK_URL=https://github.com/adoptium/temurin8-binaries/releases/download/jdk8u392-b08/OpenJDK8U-jdk_x64_windows_hotspot_8u392b08.msi
set DOWNLOAD_PATH=%TEMP%\OpenJDK8U-jdk_x64_windows.msi
set INSTALL_PATH=C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot

echo 📋 安装信息：
echo    JDK版本: OpenJDK 1.8.0_392
echo    下载路径: %DOWNLOAD_PATH%
echo    安装路径: %INSTALL_PATH%
echo.

REM 下载JDK
echo 📥 下载JDK 1.8安装包...
echo 这可能需要几分钟，请耐心等待...
echo.

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%JDK_URL%' -OutFile '%DOWNLOAD_PATH%' -UseBasicParsing}"

if not exist "%DOWNLOAD_PATH%" (
    echo ❌ 下载失败！
    echo 请检查网络连接或手动下载：
    echo %JDK_URL%
    pause
    exit /b 1
)

echo ✅ 下载完成！
echo.

REM 安装JDK
echo 🔧 开始安装JDK 1.8...
echo 安装过程中请不要关闭此窗口...
echo.

msiexec /i "%DOWNLOAD_PATH%" /quiet /norestart ADDLOCAL=FeatureMain,FeatureEnvironment,FeatureJarFileRunWith,FeatureJavaHome

if %errorLevel% neq 0 (
    echo ❌ 安装失败！
    echo 请尝试手动运行安装包：%DOWNLOAD_PATH%
    pause
    exit /b 1
)

echo ✅ JDK 1.8 安装成功！
echo.

REM 配置环境变量
echo ⚙️ 配置环境变量...

REM 尝试多个可能的安装路径
set JAVA_HOME_FOUND=0
for %%i in (
    "C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot"
    "C:\Program Files\AdoptOpenJDK\jdk-8.0.392.8-hotspot"
    "C:\Program Files\Java\jdk1.8.0_392"
    "C:\Program Files\Java\jdk-8"
) do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        set JAVA_HOME_FOUND=1
        goto :found_java_home
    )
)

:found_java_home
if %JAVA_HOME_FOUND% equ 1 (
    echo 找到JDK安装路径: %JAVA_HOME%
    
    REM 设置JAVA_HOME环境变量
    powershell -Command "[Environment]::SetEnvironmentVariable('JAVA_HOME', '%JAVA_HOME%', 'Machine')"
    
    REM 添加到PATH
    powershell -Command "$currentPath = [Environment]::GetEnvironmentVariable('Path', 'Machine'); if ($currentPath -notmatch [regex]::Escape('%JAVA_HOME%\bin')) { $newPath = '%JAVA_HOME%\bin;' + $currentPath; [Environment]::SetEnvironmentVariable('Path', $newPath, 'Machine') }"
    
    echo ✅ 环境变量配置完成！
    echo    JAVA_HOME = %JAVA_HOME%
    echo    PATH 已添加 %JAVA_HOME%\bin
) else (
    echo ⚠️ 警告：找不到JDK安装路径
    echo 请手动配置JAVA_HOME环境变量
)

echo.

REM 清理下载文件
echo 🧹 清理临时文件...
del "%DOWNLOAD_PATH%" 2>nul
echo ✅ 清理完成
echo.

REM 验证安装
echo 🔍 验证安装结果...
echo.

REM 刷新环境变量（仅对当前会话有效）
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v JAVA_HOME 2^>nul') do set "JAVA_HOME=%%b"
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v Path 2^>nul') do set "PATH=%%b"

if exist "%JAVA_HOME%\bin\java.exe" (
    echo ✅ JAVA_HOME 配置正确: %JAVA_HOME%
    echo.
    echo Java版本信息:
    "%JAVA_HOME%\bin\java.exe" -version
    echo.
    echo 🎉 JDK 1.8 安装完成！
    echo.
    echo 📝 重要提示:
    echo    1. 请重新打开命令提示符或PowerShell窗口
    echo    2. 运行 'java -version' 验证安装
    echo    3. 现在可以使用JDK 1.8来编译和运行Java程序了
) else (
    echo ⚠️ 警告：无法验证安装结果
    echo 请手动检查以下内容:
    echo    1. JAVA_HOME环境变量是否正确设置
    echo    2. PATH环境变量是否包含 %%JAVA_HOME%%\bin
    echo    3. 重启计算机后再次尝试
)

echo.
echo 安装脚本执行完成！
echo 按任意键退出...
pause >nul