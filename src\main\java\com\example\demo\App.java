package com.example.demo;

import com.example.demo.model.Device;
import com.example.demo.model.IoPoint;
import com.example.demo.parser.WordDocumentParser;
import com.example.demo.service.DeviceRecognitionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 设计院资料识别系统主应用程序
 */
public class App {

    private final WordDocumentParser documentParser;
    private final DeviceRecognitionService deviceRecognitionService;
    private final ObjectMapper objectMapper;

    public App() {
        this.documentParser = new WordDocumentParser();
        this.deviceRecognitionService = new DeviceRecognitionService();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
    }

    public static void main(String[] args) {
        System.out.println("设计院资料识别系统启动中...");

        App app = new App();
        app.run();

        System.out.println("设计院资料识别系统运行完成");
    }

    public void run() {
        System.out.println("欢迎使用设计院资料识别系统！");
        System.out.println("系统版本: 1.0.0");
        System.out.println("Java版本: " + System.getProperty("java.version"));

        // 处理测试文档
        String testFile = "test_files/7 滨江调压站IO表.docx";
        processDocument(testFile);
    }

    /**
     * 处理Word文档
     * 
     * @param filePath 文档路径
     */
    public void processDocument(String filePath) {
        System.out.println("\n开始处理文档: " + filePath);

        try {
            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                System.err.println("文件不存在: " + filePath);
                System.err.println("请确保测试文件存在，系统只处理真实的Word文档");
                return;
            }

            // 第一步：解析文档
            System.out.println("第一步：解析Word文档...");
            List<IoPoint> ioPoints = documentParser.parseDocument(filePath);
            System.out.println("解析完成，共提取 " + ioPoints.size() + " 个I/O点位");

            // 第二步：设备识别和点位绑定
            processIoPoints(ioPoints);

        } catch (IOException e) {
            System.err.println("文档处理失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("系统错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理I/O点位数据
     */
    private void processIoPoints(List<IoPoint> ioPoints) {
        System.out.println("\n开始设备识别和点位绑定...");

        // 设备识别和点位绑定
        Map<String, Device> devices = deviceRecognitionService.recognizeDevices(ioPoints);

        // 输出结果到JSON文件
        outputResults(devices);

        // 打印结果
        printResults(devices);
    }

    /**
     * 输出处理结果到JSON文件
     * 
     * @param devices 设备映射表
     */
    private void outputResults(Map<String, Device> devices) {
        try {
            // 输出到JSON文件
            File outputFile = new File("output/devices.json");
            outputFile.getParentFile().mkdirs();

            objectMapper.writeValue(outputFile, devices);
            System.out.println("结果已保存到: " + outputFile.getAbsolutePath());

        } catch (IOException e) {
            System.err.println("保存结果失败: " + e.getMessage());
        }
    }

    /**
     * 打印处理结果
     */
    private void printResults(Map<String, Device> devices) {
        System.out.println("\n=== 处理结果 ===");
        System.out.println("识别到 " + devices.size() + " 个设备");

        for (Device device : devices.values()) {
            System.out.println("\n设备ID: " + device.getDeviceId());
            System.out.println("设备类别: " + device.getCategory());
            System.out.println("别名: " + device.getAliases());

            if (!device.getSensors().isEmpty()) {
                System.out.println("传感器点位 (" + device.getSensors().size() + "个):");
                for (IoPoint sensor : device.getSensors()) {
                    System.out.println("  - " + sensor.getTag() + ": " + sensor.getDesc());
                }
            }

            if (!device.getActuators().isEmpty()) {
                System.out.println("执行器点位 (" + device.getActuators().size() + "个):");
                for (IoPoint actuator : device.getActuators()) {
                    System.out.println("  - " + actuator.getTag() + ": " + actuator.getDesc());
                }
            }

            if (!device.getStatusSignals().isEmpty()) {
                System.out.println("状态信号点位 (" + device.getStatusSignals().size() + "个):");
                for (IoPoint status : device.getStatusSignals()) {
                    System.out.println("  - " + status.getTag() + ": " + status.getDesc());
                }
            }
        }

        // 统计信息
        printStatistics(devices);
    }

    /**
     * 打印统计信息
     * 
     * @param devices 设备映射表
     */
    private void printStatistics(Map<String, Device> devices) {
        System.out.println("\n=== 处理统计 ===");
        System.out.println("设备总数: " + devices.size());

        int totalSensors = 0;
        int totalActuators = 0;
        int totalStatusSignals = 0;
        int ruleBindings = 0;
        int embedBindings = 0;
        int manualBindings = 0;

        for (Device device : devices.values()) {
            totalSensors += device.getSensors().size();
            totalActuators += device.getActuators().size();
            totalStatusSignals += device.getStatusSignals().size();

            // 统计绑定方法
            for (IoPoint point : device.getAllPoints()) {
                String method = point.getBindMethod();
                if ("RULE".equals(method))
                    ruleBindings++;
                else if ("EMBED".equals(method))
                    embedBindings++;
                else if ("MANUAL".equals(method))
                    manualBindings++;
            }
        }

        System.out.println("传感器点位: " + totalSensors);
        System.out.println("执行器点位: " + totalActuators);
        System.out.println("状态信号点位: " + totalStatusSignals);
        System.out.println("总点位数: " + (totalSensors + totalActuators + totalStatusSignals));

        System.out.println("\n=== 绑定方法统计 ===");
        System.out.println("规则绑定: " + ruleBindings);
        System.out.println("语义绑定: " + embedBindings);
        System.out.println("人工确认: " + manualBindings);

        // 显示前几个设备的详细信息
        System.out.println("\n=== 设备详情（前5个）===");
        devices.values().stream()
                .limit(5)
                .forEach(device -> {
                    System.out.println("设备ID: " + device.getDeviceId() +
                            ", 类别: " + device.getCategory() +
                            ", 点位数: " + device.getTotalPointCount());
                });
    }

}
