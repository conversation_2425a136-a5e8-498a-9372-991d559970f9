package com.example.demo.parser;

/**
 * 表格列映射类
 * 用于记录I/O表中各列的索引位置
 */
public class TableColumnMapping {
    
    private int headerRowIndex = -1;      // 表头行索引
    private int tagColumnIndex = -1;      // 位号列索引
    private int descColumnIndex = -1;     // 描述列索引
    private int signalTypeColumnIndex = -1; // 信号类型列索引
    private int unitColumnIndex = -1;     // 单位列索引
    private int rangeColumnIndex = -1;    // 量程列索引
    
    // 构造函数
    public TableColumnMapping() {}
    
    // Getter和Setter方法
    public int getHeaderRowIndex() {
        return headerRowIndex;
    }
    
    public void setHeaderRowIndex(int headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
    }
    
    public int getTagColumnIndex() {
        return tagColumnIndex;
    }
    
    public void setTagColumnIndex(int tagColumnIndex) {
        this.tagColumnIndex = tagColumnIndex;
    }
    
    public int getDescColumnIndex() {
        return descColumnIndex;
    }
    
    public void setDescColumnIndex(int descColumnIndex) {
        this.descColumnIndex = descColumnIndex;
    }
    
    public int getSignalTypeColumnIndex() {
        return signalTypeColumnIndex;
    }
    
    public void setSignalTypeColumnIndex(int signalTypeColumnIndex) {
        this.signalTypeColumnIndex = signalTypeColumnIndex;
    }
    
    public int getUnitColumnIndex() {
        return unitColumnIndex;
    }
    
    public void setUnitColumnIndex(int unitColumnIndex) {
        this.unitColumnIndex = unitColumnIndex;
    }
    
    public int getRangeColumnIndex() {
        return rangeColumnIndex;
    }
    
    public void setRangeColumnIndex(int rangeColumnIndex) {
        this.rangeColumnIndex = rangeColumnIndex;
    }
    
    /**
     * 检查映射是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return tagColumnIndex >= 0 || descColumnIndex >= 0;
    }
    
    @Override
    public String toString() {
        return "TableColumnMapping{" +
                "headerRowIndex=" + headerRowIndex +
                ", tagColumnIndex=" + tagColumnIndex +
                ", descColumnIndex=" + descColumnIndex +
                ", signalTypeColumnIndex=" + signalTypeColumnIndex +
                ", unitColumnIndex=" + unitColumnIndex +
                ", rangeColumnIndex=" + rangeColumnIndex +
                '}';
    }
}
