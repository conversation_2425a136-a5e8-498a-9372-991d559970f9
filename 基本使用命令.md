# 设计院资料识别系统 - 基本使用命令

## 系统概述
**设计院资料识别系统** 是一个专业的工业文档智能解析系统，专门用于解析设计院的I/O点位表等技术文档。

### 核心功能
- **Word文档解析**: 自动解析Word格式的I/O点位表
- **智能表头识别**: 基于语义相似度的表头自动识别
- **设备自动识别**: 根据点位信息自动识别和分类工业设备
- **点位智能绑定**: 将I/O点位自动绑定到对应设备
- **结果导出**: 生成JSON格式的结构化数据

## 环境信息
- **Java版本**: 24.0.1
- **项目类型**: 标准Maven项目结构
- **编码**: UTF-8
- **主要依赖**: Apache POI, Jackson, SLF4J

## 项目结构
```
设计院资料识别demo/
├── src/main/java/com/example/demo/
│   ├── App.java                           # 主程序入口
│   ├── DocumentRecognitionService.java    # 文档识别服务
│   ├── model/                             # 数据模型
│   │   ├── IoPoint.java                   # I/O点位模型
│   │   └── Device.java                    # 设备模型
│   ├── parser/                            # 解析器
│   │   ├── WordDocumentParser.java        # Word文档解析器
│   │   ├── SemanticHeaderDetector.java    # 语义表头检测器
│   │   ├── TableColumnMapping.java        # 表格列映射
│   │   └── ColumnRole.java                # 列角色定义
│   └── service/                           # 业务服务
│       ├── DeviceRecognitionService.java  # 设备识别服务
│       └── EmbeddingService.java          # 嵌入向量服务
├── test_files/                            # 测试文件
│   └── 7 滨江调压站IO表.docx              # 测试用Word文档
├── output/                                # 输出目录
│   └── devices.json                       # 解析结果
└── lib/                                   # 依赖库
```

## 基本使用命令

### 0. 依赖下载（首次运行）

#### 下载所有依赖JAR包
```bash
.\download-dependencies.bat
```

### 1. 编译命令

#### 编译完整项目（推荐）
```bash
javac -cp "lib/*;src/main/java" -d target/classes src/main/java/com/example/demo/App.java src/main/java/com/example/demo/model/IoPoint.java src/main/java/com/example/demo/model/Device.java src/main/java/com/example/demo/service/DeviceRecognitionService.java src/main/java/com/example/demo/service/EmbeddingService.java src/main/java/com/example/demo/parser/WordDocumentParser.java src/main/java/com/example/demo/parser/TableColumnMapping.java src/main/java/com/example/demo/parser/ColumnRole.java src/main/java/com/example/demo/parser/SemanticHeaderDetector.java
```

### 2. 运行命令

#### 运行主程序（Word文档解析）
```bash
java -cp "lib/*;target/classes" com.example.demo.App
```

### 3. 系统输出示例
```
设计院资料识别系统启动中...
欢迎使用设计院资料识别系统！
系统版本: 1.0.0
Java版本: 24.0.1

开始处理文档: test_files/7 滨江调压站IO表.docx
第一步：解析Word文档...
解析完成，共提取 111 个I/O点位

开始设备识别和点位绑定...
设备识别完成，识别设备数: 28, 绑定点位数: 111, 未绑定点位数: 0

=== 处理统计 ===
设备总数: 28
传感器点位: 22
执行器点位: 39
状态信号点位: 50
总点位数: 111

=== 绑定方法统计 ===
规则绑定: 111
语义绑定: 0
人工确认: 0
```

### 4. 目录管理命令

#### 创建编译目录
```bash
mkdir target\classes
```

#### 清理编译输出
```bash
rmdir /s target
```

### 5. 完整操作流程

#### 从零开始编译运行
```bash
# 1. 下载依赖（首次运行）
.\download-dependencies.bat

# 2. 创建编译目录
mkdir target\classes

# 3. 编译主程序
javac -cp "lib/*;src/main/java" -d target/classes src/main/java/com/example/demo/App.java src/main/java/com/example/demo/model/IoPoint.java src/main/java/com/example/demo/model/Device.java src/main/java/com/example/demo/service/DeviceRecognitionService.java src/main/java/com/example/demo/service/EmbeddingService.java src/main/java/com/example/demo/parser/WordDocumentParser.java src/main/java/com/example/demo/parser/TableColumnMapping.java src/main/java/com/example/demo/parser/ColumnRole.java src/main/java/com/example/demo/parser/SemanticHeaderDetector.java

# 4. 运行主程序
java -cp "lib/*;target/classes" com.example.demo.App
```

## 配置说明

### 修改测试文件
要处理不同的Word文档，修改 `src/main/java/com/example/demo/App.java` 第48行：
```java
String testFile = "test_files/你的文档.docx";  // 修改这里
```

### 输出文件位置
- **解析结果**: `output/devices.json`
- **日志输出**: 控制台显示

### 支持的文档格式
- **Word文档**: `.docx` 格式
- **表格要求**: 包含仪表位号、点位描述等标准列
- **编码要求**: UTF-8编码

## 系统特性

### 智能解析能力
- **语义表头识别**: 自动识别不同格式的表头
- **分组标识过滤**: 自动过滤"BPCS"、"ESD"、"RS485"等分组标识
- **签名行过滤**: 自动过滤"设计"、"校对"、"审核"等签名信息

### 设备识别能力
- **报警装置**: HS-、AA-开头的点位
- **阀门设备**: XZ-、XC-、EXC-开头的点位
- **泵设备**: 各种泵相关的控制和监测点位
- **加热器**: 加热系统相关点位
- **过滤器**: 过滤器相关点位
- **通用设备**: 其他工业设备

### 点位分类
- **传感器点位**: AI、TT、PT、PdT等测量点位
- **执行器点位**: AO、XZ、XC、XO等控制点位
- **状态信号点位**: DI、ZSH、ZSL、UA等状态点位

## 命令说明

- `-cp`: 指定classpath（类路径）
- `-d`: 指定编译输出目录
- `target/classes`: 主程序编译输出目录
- `;`: Windows下classpath分隔符（Linux/Mac使用`:`）
- `lib/*`: 引用lib目录下的所有JAR包

## 注意事项

1. **首次运行**: 必须先执行 `.\download-dependencies.bat` 下载依赖
2. **编译顺序**: 必须按照依赖关系编译所有相关类
3. **文件编码**: 确保Word文档使用UTF-8编码
4. **目录结构**: 确保test_files和output目录存在
5. **Java版本**: 需要Java 24.0.1或更高版本

## 故障排除

### 常见问题
1. **编译失败**: 检查是否下载了所有依赖JAR包
2. **运行失败**: 检查classpath是否包含lib目录
3. **解析失败**: 检查Word文档格式是否符合要求
4. **中文乱码**: 确保系统编码设置为UTF-8

### 调试模式
如需查看详细日志，可以修改日志级别或添加调试输出。
