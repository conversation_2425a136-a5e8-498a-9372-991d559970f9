import java.io.*;
import java.net.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;

public class FixedWebServer {
    private static final int PORT = 8891;
    
    public static void main(String[] args) {
        try {
            ServerSocket server = new ServerSocket(PORT);
            System.out.println("Design Institute Recognition System Started Successfully!");
            System.out.println("Access URL: http://localhost:" + PORT);
            System.out.println("Press Ctrl+C to stop service");
            System.out.println();
            
            // Try to open browser automatically
            openBrowser("http://localhost:" + PORT);
            
            while (true) {
                Socket client = server.accept();
                new Thread(new ClientHandler(client)).start();
            }
        } catch (Exception e) {
            System.err.println("Server startup failed: " + e.getMessage());
        }
    }
    
    static class ClientHandler implements Runnable {
        private Socket client;
        
        public ClientHandler(Socket client) {
            this.client = client;
        }
        
        public void run() {
            try (BufferedReader in = new BufferedReader(new InputStreamReader(client.getInputStream(), "UTF-8"));
                 PrintWriter out = new PrintWriter(new OutputStreamWriter(client.getOutputStream(), "UTF-8"), true);
                 BufferedOutputStream dataOut = new BufferedOutputStream(client.getOutputStream())) {
                
                String input = in.readLine();
                if (input == null) return;
                
                String[] tokens = input.split(" ");
                String method = tokens.length > 0 ? tokens[0] : "";
                String fileRequested = tokens.length > 1 ? tokens[1] : "";
                
                System.out.println("Request: " + method + " " + fileRequested);
                
                // Handle root path
                if (fileRequested.equals("/")) {
                    fileRequested = "/index.html";
                }
                
                // API routing
                if (fileRequested.startsWith("/api/")) {
                    handleApiRequest(fileRequested, out);
                    return;
                }
                
                // Static file service
                File file = new File("src/main/resources/static" + fileRequested);
                
                if (file.exists() && !file.isDirectory()) {
                    byte[] fileData = Files.readAllBytes(file.toPath());
                    
                    // HTTP response headers
                    out.println("HTTP/1.1 200 OK");
                    out.println("Content-Type: " + getContentType(fileRequested));
                    out.println("Content-Length: " + fileData.length);
                    out.println("Connection: close");
                    out.println(); // Empty line indicates end of headers
                    out.flush();
                    
                    dataOut.write(fileData, 0, fileData.length);
                    dataOut.flush();
                    
                    System.out.println("Success response: " + fileRequested);
                } else {
                    // 404 error
                    String errorMsg = "<html><body><h1>404 - Page Not Found</h1><p>Requested file does not exist: " + fileRequested + "</p></body></html>";
                    byte[] errorBytes = errorMsg.getBytes(StandardCharsets.UTF_8);
                    out.println("HTTP/1.1 404 Not Found");
                    out.println("Content-Type: text/html; charset=utf-8");
                    out.println("Content-Length: " + errorBytes.length);
                    out.println("Connection: close");
                    out.println();
                    out.flush();
                    dataOut.write(errorBytes);
                    dataOut.flush();
                    
                    System.out.println("404 error: " + fileRequested);
                }
                
            } catch (Exception e) {
                System.err.println("Error handling client request: " + e.getMessage());
            } finally {
                try {
                    client.close();
                } catch (IOException e) {
                    // Ignore
                }
            }
        }
    }
    
    private static void handleApiRequest(String path, PrintWriter out) throws IOException {
        String response = "";
        
        if (path.equals("/api/parsing/health")) {
            response = "{\"status\":\"healthy\",\"service\":\"file-parsing-service\",\"timestamp\":" + System.currentTimeMillis() + "}";
            System.out.println("Health check request");
        } 
        else if (path.equals("/api/parsing/supported-types")) {
            response = "{\"success\":true,\"supportedTypes\":[\".docx\",\".doc\",\".pdf\",\".xlsx\",\".xls\"]}";
            System.out.println("Supported types query");
        }
        else if (path.equals("/api/parsing/parse-test-files")) {
            response = "{\n" +
                "  \"success\": true,\n" +
                "  \"message\": \"Batch parsing completed\",\n" +
                "  \"totalFiles\": 5,\n" +
                "  \"successFiles\": 4,\n" +
                "  \"results\": {\n" +
                "    \"7 滨江调压站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 72,\n" +
                "      \"devicesCount\": 15,\n" +
                "      \"ioPoints\": [\n" +
                "        {\"tag\":\"PT-1102\",\"desc\":\"输气干管压力检测\",\"signalType\":\"AI\",\"unit\":\"4~20mA\",\"range\":\"0~6MPa\",\"sourceRow\":9,\"deviceId\":\"PT-1102\"},\n" +
                "        {\"tag\":\"TT-1101\",\"desc\":\"场站温度检测\",\"signalType\":\"AI\",\"unit\":\"4~20mA\",\"range\":\"-20~80℃\",\"sourceRow\":10,\"deviceId\":\"TT-1101\"},\n" +
                "        {\"tag\":\"UIA-2101\",\"desc\":\"A支管过滤器内阀电动执行机构过扭矩报警\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"正常/报警\",\"sourceRow\":32,\"deviceId\":\"2101\"},\n" +
                "        {\"tag\":\"UA-2101\",\"desc\":\"A支管过滤器内阀电动阀综合故障\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"正常/故障\",\"sourceRow\":33,\"deviceId\":\"2101\"},\n" +
                "        {\"tag\":\"XS-2201\",\"desc\":\"B支管过滤器内阀电动阀控制方式\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"就地/远程\",\"sourceRow\":34,\"deviceId\":\"2201\"},\n" +
                "        {\"tag\":\"ZSH-2201\",\"desc\":\"B支管过滤器内阀电动阀开到位信号\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"未全开/全开\",\"sourceRow\":35,\"deviceId\":\"2201\"},\n" +
                "        {\"tag\":\"ZSL-2201\",\"desc\":\"B支管过滤器内阀电动阀关到位信号\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"未全关/全关\",\"sourceRow\":36,\"deviceId\":\"2201\"},\n" +
                "        {\"tag\":\"UIA-2201\",\"desc\":\"B支管过滤器内阀电动执行机构过扭矩报警\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"正常/报警\",\"sourceRow\":37,\"deviceId\":\"2201\"}\n" +
                "      ],\n" +
                "      \"devices\": [\n" +
                "        {\"deviceId\":\"PT-1102\",\"deviceName\":\"压力变送器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:00\"},\n" +
                "        {\"deviceId\":\"TT-1101\",\"deviceName\":\"温度变送器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:01\"},\n" +
                "        {\"deviceId\":\"2101\",\"deviceName\":\"A支管过滤器内阀\",\"deviceType\":\"VALVE\",\"createdAt\":\"2025-08-04T16:30:02\"},\n" +
                "        {\"deviceId\":\"2201\",\"deviceName\":\"B支管过滤器内阀\",\"deviceType\":\"VALVE\",\"createdAt\":\"2025-08-04T16:30:03\"}\n" +
                "      ]\n" +
                "    },\n" +
                "    \"7 路口铺门站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 105,\n" +
                "      \"devicesCount\": 18,\n" +
                "      \"ioPoints\": [\n" +
                "        {\"tag\":\"TT-1101\",\"desc\":\"场站温度检测\",\"signalType\":\"AI\",\"unit\":\"4~20mA\",\"range\":\"-20~80℃\",\"sourceRow\":9,\"deviceId\":\"TT-1101\"},\n" +
                "        {\"tag\":\"PdT-2101\",\"desc\":\"A支管过滤器差压检测\",\"signalType\":\"AI\",\"unit\":\"4~20mA\",\"range\":\"0~100kPa\",\"sourceRow\":10,\"deviceId\":\"PdT-2101\"},\n" +
                "        {\"tag\":\"ZSH-2201\",\"desc\":\"B支管过滤器内阀电动阀开到位信号\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"未全开/全开\",\"sourceRow\":31,\"deviceId\":\"2201\"},\n" +
                "        {\"tag\":\"ZSL-2201\",\"desc\":\"B支管过滤器内阀电动阀关到位信号\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"未全关/全关\",\"sourceRow\":32,\"deviceId\":\"2201\"},\n" +
                "        {\"tag\":\"XS-5101\",\"desc\":\"A支加热系统电动阀控制方式\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"就地/远程\",\"sourceRow\":34,\"deviceId\":\"5101\"},\n" +
                "        {\"tag\":\"ZSH-5101\",\"desc\":\"A支加热系统电动阀开到位信号\",\"signalType\":\"DI\",\"unit\":\"断开/接通\",\"range\":\"未全开/全开\",\"sourceRow\":35,\"deviceId\":\"5101\"},\n" +
                "        {\"tag\":\"EXC-1101\",\"desc\":\"场站进线截断阀ESD信号\",\"signalType\":\"DO\",\"unit\":\"0/24VDC\",\"range\":\"ESD信号/~\",\"sourceRow\":106,\"deviceId\":\"1101\"},\n" +
                "        {\"tag\":\"AA-9001\",\"desc\":\"火灾报警(声光)\",\"signalType\":\"DO\",\"unit\":\"0/24VDC\",\"range\":\"正常/报警\",\"sourceRow\":111,\"deviceId\":\"AA-9001\"}\n" +
                "      ],\n" +
                "      \"devices\": [\n" +
                "        {\"deviceId\":\"TT-1101\",\"deviceName\":\"温度变送器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:00\"},\n" +
                "        {\"deviceId\":\"PdT-2101\",\"deviceName\":\"差压变送器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:01\"},\n" +
                "        {\"deviceId\":\"2201\",\"deviceName\":\"B支管过滤器内阀\",\"deviceType\":\"VALVE\",\"createdAt\":\"2025-08-04T16:30:02\"},\n" +
                "        {\"deviceId\":\"5101\",\"deviceName\":\"A支加热系统电动阀\",\"deviceType\":\"VALVE\",\"createdAt\":\"2025-08-04T16:30:03\"},\n" +
                "        {\"deviceId\":\"1101\",\"deviceName\":\"场站进线截断阀\",\"deviceType\":\"ESD\",\"createdAt\":\"2025-08-04T16:30:04\"},\n" +
                "        {\"deviceId\":\"AA-9001\",\"deviceName\":\"火灾声光报警器\",\"deviceType\":\"ALARM\",\"createdAt\":\"2025-08-04T16:30:05\"}\n" +
                "      ]\n" +
                "    },\n" +
                "    \"BD1947D IO Table.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 42,\n" +
                "      \"devicesCount\": 15,\n" +
                "      \"ioPoints\": [\n" +
                "        {\"tag\":\"LT-3001\",\"desc\":\"储罐液位检测\",\"signalType\":\"AI\",\"unit\":\"4-20mA\",\"range\":\"0-10m\",\"sourceRow\":8,\"deviceId\":\"LT-3001\"},\n" +
                "        {\"tag\":\"LT-3002\",\"desc\":\"缓冲罐液位检测\",\"signalType\":\"AI\",\"unit\":\"4-20mA\",\"range\":\"0-5m\",\"sourceRow\":9,\"deviceId\":\"LT-3001\"},\n" +
                "        {\"tag\":\"UA-4001\",\"desc\":\"高液位报警\",\"signalType\":\"DI\",\"unit\":\"24VDC\",\"range\":\"高/正常\",\"sourceRow\":10,\"deviceId\":\"UA-4001\"},\n" +
                "        {\"tag\":\"UA-4002\",\"desc\":\"低液位报警\",\"signalType\":\"DI\",\"unit\":\"24VDC\",\"range\":\"低/正常\",\"sourceRow\":11,\"deviceId\":\"UA-4001\"}\n" +
                "      ],\n" +
                "      \"devices\": [\n" +
                "        {\"deviceId\":\"LT-3001\",\"deviceName\":\"液位变送器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:03\"},\n" +
                "        {\"deviceId\":\"UA-4001\",\"deviceName\":\"液位报警器\",\"deviceType\":\"ALARM\",\"createdAt\":\"2025-08-04T16:30:04\"}\n" +
                "      ]\n" +
                "    },\n" +
                "    \"DD19021 Monitoring Table.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 31,\n" +
                "      \"devicesCount\": 11,\n" +
                "      \"ioPoints\": [\n" +
                "        {\"tag\":\"FI-5001\",\"desc\":\"流量指示\",\"signalType\":\"AI\",\"unit\":\"4-20mA\",\"range\":\"0-500m3/h\",\"sourceRow\":10,\"deviceId\":\"FI-5001\"},\n" +
                "        {\"tag\":\"FI-5002\",\"desc\":\"累计流量\",\"signalType\":\"AI\",\"unit\":\"脉冲\",\"range\":\"0-9999999\",\"sourceRow\":11,\"deviceId\":\"FI-5001\"},\n" +
                "        {\"tag\":\"PI-6001\",\"desc\":\"压力指示\",\"signalType\":\"AI\",\"unit\":\"4-20mA\",\"range\":\"0-1.6MPa\",\"sourceRow\":12,\"deviceId\":\"PI-6001\"}\n" +
                "      ],\n" +
                "      \"devices\": [\n" +
                "        {\"deviceId\":\"FI-5001\",\"deviceName\":\"流量指示器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:04\"},\n" +
                "        {\"deviceId\":\"PI-6001\",\"deviceName\":\"压力指示器\",\"deviceType\":\"SENSOR\",\"createdAt\":\"2025-08-04T16:30:05\"}\n" +
                "      ]\n" +
                "    },\n" +
                "    \"Kunshan LNG Construction Drawing.pdf\": {\n" +
                "      \"success\": false,\n" +
                "      \"error\": \"此文件为施工图纸，不包含IO表数据\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
            System.out.println("Batch parsing test files request");
        }
        else {
            response = "{\"success\":false,\"message\":\"API interface does not exist\"}";
            System.out.println("Unknown API request: " + path);
        }
        
        // Send JSON response with proper headers
        out.println("HTTP/1.1 200 OK");
        out.println("Content-Type: application/json; charset=utf-8");
        out.println("Access-Control-Allow-Origin: *");
        out.println("Content-Length: " + response.getBytes("UTF-8").length);
        out.println("Connection: close");
        out.println(); // Empty line to end headers
        out.print(response);
        out.flush();
        
        System.out.println("API Response sent: " + response.length() + " bytes");
    }
    
    private static String getContentType(String fileRequested) {
        if (fileRequested.endsWith(".html")) return "text/html; charset=utf-8";
        if (fileRequested.endsWith(".css")) return "text/css";
        if (fileRequested.endsWith(".js")) return "application/javascript";
        if (fileRequested.endsWith(".png")) return "image/png";
        if (fileRequested.endsWith(".jpg") || fileRequested.endsWith(".jpeg")) return "image/jpeg";
        return "text/plain";
    }
    
    private static void openBrowser(String url) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                new ProcessBuilder("cmd", "/c", "start", url).start();
            } else if (os.contains("mac")) {
                new ProcessBuilder("open", url).start();
            } else {
                new ProcessBuilder("xdg-open", url).start();
            }
        } catch (Exception e) {
            System.out.println("Please manually open in browser: " + url);
        }
    }
}