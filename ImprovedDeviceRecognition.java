import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 改进的设备识别算法
 * 基于真实的Word文档分析优化设备识别逻辑
 */
public class ImprovedDeviceRecognition {
    
    // 改进的设备编号模式，支持更复杂的格式
    private static final Pattern DEVICE_PATTERN = Pattern.compile("([A-Z]+)-?(\\d{4})(?:-?(\\d+))?");
    
    // 设备分组规则
    private static final Map<String, String> PREFIX_TO_DEVICE_TYPE = new HashMap<>();
    static {
        // 压力类设备
        PREFIX_TO_DEVICE_TYPE.put("PT", "压力变送器");
        PREFIX_TO_DEVICE_TYPE.put("PI", "压力指示器");
        PREFIX_TO_DEVICE_TYPE.put("PdT", "差压变送器");
        
        // 温度类设备  
        PREFIX_TO_DEVICE_TYPE.put("TT", "温度变送器");
        PREFIX_TO_DEVICE_TYPE.put("TI", "温度指示器");
        
        // 流量类设备
        PREFIX_TO_DEVICE_TYPE.put("FT", "流量变送器");
        PREFIX_TO_DEVICE_TYPE.put("FI", "流量指示器");
        
        // 液位类设备
        PREFIX_TO_DEVICE_TYPE.put("LT", "液位变送器");
        PREFIX_TO_DEVICE_TYPE.put("LI", "液位指示器");
        
        // 阀门控制设备（这些应该归为一个设备的不同点位）
        PREFIX_TO_DEVICE_TYPE.put("XS", "电动阀控制");
        PREFIX_TO_DEVICE_TYPE.put("XO", "电动阀开启");
        PREFIX_TO_DEVICE_TYPE.put("XC", "电动阀关闭");
        PREFIX_TO_DEVICE_TYPE.put("ZSH", "电动阀开到位");
        PREFIX_TO_DEVICE_TYPE.put("ZSL", "电动阀关到位");
        PREFIX_TO_DEVICE_TYPE.put("UIA", "电动阀过扭矩");
        PREFIX_TO_DEVICE_TYPE.put("UA", "电动阀故障");
        
        // ESD系统
        PREFIX_TO_DEVICE_TYPE.put("EXC", "ESD紧急切断");
        PREFIX_TO_DEVICE_TYPE.put("EXO", "ESD开启输出");
        
        // 检测开关
        PREFIX_TO_DEVICE_TYPE.put("DS", "差压开关");
        PREFIX_TO_DEVICE_TYPE.put("YS", "通球检测器");
        
        // 状态指示
        PREFIX_TO_DEVICE_TYPE.put("ZI", "位置指示器");
        
        // 可燃气体检测
        PREFIX_TO_DEVICE_TYPE.put("SD", "气体探测器");
        PREFIX_TO_DEVICE_TYPE.put("SH", "气体探测器");
        
        // 报警器
        PREFIX_TO_DEVICE_TYPE.put("AA", "声光报警器");
    }
    
    // 需要合并为同一设备的前缀组
    private static final Map<String, Set<String>> DEVICE_GROUPS = new HashMap<>();
    static {
        // 电动阀相关的所有点位都应该归为同一个设备
        Set<String> valveGroup = new HashSet<>(Arrays.asList("XS", "XO", "XC", "ZSH", "ZSL", "UIA", "UA"));
        DEVICE_GROUPS.put("VALVE", valveGroup);
        
        // 气体检测器相关点位
        Set<String> gasDetectorGroup = new HashSet<>(Arrays.asList("SD", "SH"));
        DEVICE_GROUPS.put("GAS_DETECTOR", gasDetectorGroup);
        
        // ESD系统相关点位
        Set<String> esdGroup = new HashSet<>(Arrays.asList("EXC", "EXO"));
        DEVICE_GROUPS.put("ESD", esdGroup);
    }
    
    /**
     * 改进的设备识别算法
     */
    public static Map<String, DeviceInfo> recognizeDevicesFromTags(List<String> tags) {
        Map<String, DeviceInfo> devices = new HashMap<>();
        
        for (String tag : tags) {
            if (tag == null || tag.trim().isEmpty()) continue;
            
            // 跳过分组标识
            if (tag.equals("BPCS")) continue;
            
            String upperTag = tag.trim().toUpperCase();
            Matcher matcher = DEVICE_PATTERN.matcher(upperTag);
            
            if (matcher.matches()) {
                String prefix = matcher.group(1);
                String deviceNumber = matcher.group(2);
                String subNumber = matcher.group(3);
                
                // 确定设备ID：对于需要合并的设备组，使用设备号作为ID
                String deviceId = determineDeviceId(prefix, deviceNumber);
                String deviceType = determineDeviceType(prefix, deviceNumber);
                
                DeviceInfo device = devices.computeIfAbsent(deviceId, id -> {
                    DeviceInfo info = new DeviceInfo();
                    info.deviceId = id;
                    info.deviceType = deviceType;
                    info.deviceNumber = deviceNumber;
                    info.ioPoints = new ArrayList<>();
                    return info;
                });
                
                // 添加IO点位信息
                IoPointInfo pointInfo = new IoPointInfo();
                pointInfo.tag = tag;
                pointInfo.prefix = prefix;
                pointInfo.pointType = PREFIX_TO_DEVICE_TYPE.getOrDefault(prefix, "未知类型");
                device.ioPoints.add(pointInfo);
            }
        }
        
        return devices;
    }
    
    /**
     * 确定设备ID
     */
    private static String determineDeviceId(String prefix, String deviceNumber) {
        // 检查是否属于需要合并的设备组
        for (Map.Entry<String, Set<String>> entry : DEVICE_GROUPS.entrySet()) {
            if (entry.getValue().contains(prefix)) {
                // 对于电动阀等复合设备，使用设备号作为ID
                return deviceNumber;
            }
        }
        
        // 对于独立设备（如传感器），使用完整标签作为ID
        return prefix + "-" + deviceNumber;
    }
    
    /**
     * 确定设备类型
     */
    private static String determineDeviceType(String prefix, String deviceNumber) {
        // 检查是否属于设备组
        for (Map.Entry<String, Set<String>> entry : DEVICE_GROUPS.entrySet()) {
            if (entry.getValue().contains(prefix)) {
                switch (entry.getKey()) {
                    case "VALVE":
                        return "电动阀";
                    case "GAS_DETECTOR":
                        return "可燃气体检测器";
                    case "ESD":
                        return "ESD安全系统";
                }
            }
        }
        
        // 独立设备直接返回对应类型
        return PREFIX_TO_DEVICE_TYPE.getOrDefault(prefix, "未知设备");
    }
    
    // 设备信息类
    public static class DeviceInfo {
        public String deviceId;
        public String deviceType;
        public String deviceNumber;
        public List<IoPointInfo> ioPoints;
        
        @Override
        public String toString() {
            return "Device{id=" + deviceId + ", type=" + deviceType + ", points=" + ioPoints.size() + "}";
        }
    }
    
    // IO点位信息类
    public static class IoPointInfo {
        public String tag;
        public String prefix;
        public String pointType;
        
        @Override
        public String toString() {
            return "Point{tag=" + tag + ", type=" + pointType + "}";
        }
    }
    
    // 测试方法
    public static void main(String[] args) {
        // 基于真实Word文档的测试数据
        List<String> testTags = Arrays.asList(
            "PT-1102", "TT-1101",           // 传感器
            "UIA-2101", "UA-2101",          // 阀门故障信号
            "XS-2201", "ZSH-2201", "ZSL-2201", "UIA-2201",  // 2201号电动阀
            "XS-5101", "ZSH-5101", "ZSL-5101", "UIA-5101",  // 5101号电动阀
            "SD-0001-1", "SH-0001-1",       // 气体检测器
            "ZI-0001",                      // 位置指示
            "EXC-1101", "EXO-1105",         // ESD系统
            "AA-9001"                       // 报警器
        );
        
        Map<String, DeviceInfo> devices = recognizeDevicesFromTags(testTags);
        
        System.out.println("=== 设备识别结果 ===");
        for (DeviceInfo device : devices.values()) {
            System.out.println("\n设备ID: " + device.deviceId);
            System.out.println("设备类型: " + device.deviceType);
            System.out.println("关联点位:");
            for (IoPointInfo point : device.ioPoints) {
                System.out.println("  - " + point.tag + " (" + point.pointType + ")");
            }
        }
    }
}