{"9201": {"allPoints": [{"tag": "HS-9201", "desc": "非消防电源切断信号", "signalType": null, "unit": "", "range": "非消防电源切断信号", "rawRowId": null, "deviceId": "9201", "bindMethod": "RULE", "sourceRow": 44}], "totalPointCount": 1, "deviceId": "9201", "category": "报警装置", "aliases": ["HS-9201"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "HS-9201", "desc": "非消防电源切断信号", "signalType": null, "unit": "", "range": "非消防电源切断信号", "rawRowId": null, "deviceId": "9201", "bindMethod": "RULE", "sourceRow": 44}], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "9003": {"allPoints": [{"tag": "HS-9003", "desc": "站场一级关断按钮(工艺区)", "signalType": null, "unit": "", "range": "站场一级关断按钮(工艺区)", "rawRowId": null, "deviceId": "9003", "bindMethod": "RULE", "sourceRow": 34}, {"tag": "AA-9003", "desc": "声光报警(工艺区)", "signalType": null, "unit": "", "range": "声光报警(工艺区)", "rawRowId": null, "deviceId": "9003", "bindMethod": "RULE", "sourceRow": 42}], "totalPointCount": 2, "deviceId": "9003", "category": "报警装置", "aliases": ["HS-9003", "AA-9003"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "HS-9003", "desc": "站场一级关断按钮(工艺区)", "signalType": null, "unit": "", "range": "站场一级关断按钮(工艺区)", "rawRowId": null, "deviceId": "9003", "bindMethod": "RULE", "sourceRow": 34}, {"tag": "AA-9003", "desc": "声光报警(工艺区)", "signalType": null, "unit": "", "range": "声光报警(工艺区)", "rawRowId": null, "deviceId": "9003", "bindMethod": "RULE", "sourceRow": 42}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "9004": {"allPoints": [{"tag": "HS-9004", "desc": "站场一级关断按钮(橇装机柜间)", "signalType": null, "unit": "", "range": "站场一级关断按钮(橇装机柜间)", "rawRowId": null, "deviceId": "9004", "bindMethod": "RULE", "sourceRow": 35}, {"tag": "AA-9004", "desc": "声光报警(橇装机柜间)", "signalType": null, "unit": "", "range": "声光报警(橇装机柜间)", "rawRowId": null, "deviceId": "9004", "bindMethod": "RULE", "sourceRow": 43}], "totalPointCount": 2, "deviceId": "9004", "category": "报警装置", "aliases": ["HS-9004", "AA-9004"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "HS-9004", "desc": "站场一级关断按钮(橇装机柜间)", "signalType": null, "unit": "", "range": "站场一级关断按钮(橇装机柜间)", "rawRowId": null, "deviceId": "9004", "bindMethod": "RULE", "sourceRow": 35}, {"tag": "AA-9004", "desc": "声光报警(橇装机柜间)", "signalType": null, "unit": "", "range": "声光报警(橇装机柜间)", "rawRowId": null, "deviceId": "9004", "bindMethod": "RULE", "sourceRow": 43}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "9001": {"allPoints": [{"tag": "HS-9001", "desc": "站场一级关断按钮(大门)", "signalType": null, "unit": "", "range": "站场一级关断按钮(大门)", "rawRowId": null, "deviceId": "9001", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "AA-9001", "desc": "声光报警(大门)", "signalType": null, "unit": "", "range": "声光报警(大门)", "rawRowId": null, "deviceId": "9001", "bindMethod": "RULE", "sourceRow": 40}], "totalPointCount": 2, "deviceId": "9001", "category": "报警装置", "aliases": ["HS-9001", "AA-9001"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "HS-9001", "desc": "站场一级关断按钮(大门)", "signalType": null, "unit": "", "range": "站场一级关断按钮(大门)", "rawRowId": null, "deviceId": "9001", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "AA-9001", "desc": "声光报警(大门)", "signalType": null, "unit": "", "range": "声光报警(大门)", "rawRowId": null, "deviceId": "9001", "bindMethod": "RULE", "sourceRow": 40}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "9002": {"allPoints": [{"tag": "HS-9002", "desc": "站场一级关断按钮(逃生门)", "signalType": null, "unit": "", "range": "站场一级关断按钮(逃生门)", "rawRowId": null, "deviceId": "9002", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "AA-9002", "desc": "声光报警(工艺区)", "signalType": null, "unit": "", "range": "声光报警(工艺区)", "rawRowId": null, "deviceId": "9002", "bindMethod": "RULE", "sourceRow": 41}], "totalPointCount": 2, "deviceId": "9002", "category": "报警装置", "aliases": ["HS-9002", "AA-9002"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "HS-9002", "desc": "站场一级关断按钮(逃生门)", "signalType": null, "unit": "", "range": "站场一级关断按钮(逃生门)", "rawRowId": null, "deviceId": "9002", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "AA-9002", "desc": "声光报警(工艺区)", "signalType": null, "unit": "", "range": "声光报警(工艺区)", "rawRowId": null, "deviceId": "9002", "bindMethod": "RULE", "sourceRow": 41}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "DS-1101": {"allPoints": [{"tag": "DS-1101", "desc": "机柜间行程开关", "signalType": null, "unit": "", "range": "机柜间行程开关", "rawRowId": null, "deviceId": "DS-1101", "bindMethod": "RULE", "sourceRow": 40}], "totalPointCount": 1, "deviceId": "DS-1101", "category": "差压开关", "aliases": ["DS-1101"], "sensors": [{"tag": "DS-1101", "desc": "机柜间行程开关", "signalType": null, "unit": "", "range": "机柜间行程开关", "rawRowId": null, "deviceId": "DS-1101", "bindMethod": "RULE", "sourceRow": 40}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "3101": {"allPoints": [{"tag": "XS-3101", "desc": "加热器前电动阀控制方式", "signalType": null, "unit": "", "range": "加热器前电动阀控制方式", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 12}, {"tag": "XO-3101", "desc": "加热器前电动阀开控制", "signalType": null, "unit": "", "range": "加热器前电动阀开控制", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 16}, {"tag": "XC-3101", "desc": "加热器前电动阀关控制", "signalType": null, "unit": "", "range": "加热器前电动阀关控制", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 17}, {"tag": "ZSH-3101", "desc": "加热器前电动阀开到位检测", "signalType": null, "unit": "", "range": "加热器前电动阀开到位检测", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 13}, {"tag": "ZSL-3101", "desc": "加热器前电动阀关到位检测", "signalType": null, "unit": "", "range": "加热器前电动阀关到位检测", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "UIA-3101", "desc": "加热器前电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "加热器前电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 15}, {"tag": "UA-3101", "desc": "加热器前电动阀综合故障", "signalType": null, "unit": "", "range": "加热器前电动阀综合故障", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 16}], "totalPointCount": 7, "deviceId": "3101", "category": "电动阀", "aliases": ["XS-3101", "ZSH-3101", "ZSL-3101", "UIA-3101", "UA-3101", "XO-3101", "XC-3101"], "sensors": [], "actuators": [{"tag": "XS-3101", "desc": "加热器前电动阀控制方式", "signalType": null, "unit": "", "range": "加热器前电动阀控制方式", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 12}, {"tag": "XO-3101", "desc": "加热器前电动阀开控制", "signalType": null, "unit": "", "range": "加热器前电动阀开控制", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 16}, {"tag": "XC-3101", "desc": "加热器前电动阀关控制", "signalType": null, "unit": "", "range": "加热器前电动阀关控制", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 17}], "statusSignals": [{"tag": "ZSH-3101", "desc": "加热器前电动阀开到位检测", "signalType": null, "unit": "", "range": "加热器前电动阀开到位检测", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 13}, {"tag": "ZSL-3101", "desc": "加热器前电动阀关到位检测", "signalType": null, "unit": "", "range": "加热器前电动阀关到位检测", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "UIA-3101", "desc": "加热器前电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "加热器前电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 15}, {"tag": "UA-3101", "desc": "加热器前电动阀综合故障", "signalType": null, "unit": "", "range": "加热器前电动阀综合故障", "rawRowId": null, "deviceId": "3101", "bindMethod": "RULE", "sourceRow": 16}], "createdAt": [2025, 8, 4, 13, 41, 16, 390978200]}, "PT-1101": {"allPoints": [{"tag": "PT-1101", "desc": "进站放空压力检测", "signalType": null, "unit": "MPa", "range": "进站放空压力检测", "rawRowId": null, "deviceId": "PT-1101", "bindMethod": "RULE", "sourceRow": 26}], "totalPointCount": 1, "deviceId": "PT-1101", "category": "压力变送器", "aliases": ["PT-1101"], "sensors": [{"tag": "PT-1101", "desc": "进站放空压力检测", "signalType": null, "unit": "MPa", "range": "进站放空压力检测", "rawRowId": null, "deviceId": "PT-1101", "bindMethod": "RULE", "sourceRow": 26}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "PT-1102": {"allPoints": [{"tag": "PT-1102", "desc": "收球筒出口压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-1102", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "PT-1102", "desc": "进站压力检测", "signalType": null, "unit": "MPa", "range": "进站压力检测", "rawRowId": null, "deviceId": "PT-1102", "bindMethod": "RULE", "sourceRow": 27}], "totalPointCount": 2, "deviceId": "PT-1102", "category": "压力变送器", "aliases": ["PT-1102"], "sensors": [{"tag": "PT-1102", "desc": "收球筒出口压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-1102", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "PT-1102", "desc": "进站压力检测", "signalType": null, "unit": "MPa", "range": "进站压力检测", "rawRowId": null, "deviceId": "PT-1102", "bindMethod": "RULE", "sourceRow": 27}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 388119500]}, "0004": {"allPoints": [], "totalPointCount": 0, "deviceId": "0004", "category": "通用设备", "aliases": ["RS-0004"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "0005": {"allPoints": [], "totalPointCount": 0, "deviceId": "0005", "category": "通用设备", "aliases": ["RS-0005"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "0002": {"allPoints": [], "totalPointCount": 0, "deviceId": "0002", "category": "通用设备", "aliases": ["RS-0002"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "0003": {"allPoints": [], "totalPointCount": 0, "deviceId": "0003", "category": "通用设备", "aliases": ["RS-0003"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "2201": {"allPoints": [{"tag": "PdT-2201", "desc": "过滤器B差压检测", "signalType": null, "unit": "kPa", "range": null, "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 12}, {"tag": "XS-2201", "desc": "B套过滤器出口电动阀控制方式", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀控制方式", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 7}, {"tag": "XO-2201", "desc": "B套过滤器出口电动阀开控制", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀开控制", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "XC-2201", "desc": "B套过滤器出口电动阀关控制", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀关控制", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 15}, {"tag": "ZSH-2201", "desc": "B套过滤器出口电动阀开到位检测", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀开到位检测", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 8}, {"tag": "ZSL-2201", "desc": "B套过滤器出口电动阀关到位检测", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀关到位检测", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "UIA-2201", "desc": "B套过滤器出口电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "B套过滤器出口电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 10}, {"tag": "UA-2201", "desc": "B套过滤器出口电动阀综合故障", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀综合故障", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 11}], "totalPointCount": 8, "deviceId": "2201", "category": "电动阀", "aliases": ["PdT-2201", "XS-2201", "ZSH-2201", "ZSL-2201", "UIA-2201", "UA-2201", "XO-2201", "XC-2201"], "sensors": [{"tag": "PdT-2201", "desc": "过滤器B差压检测", "signalType": null, "unit": "kPa", "range": null, "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 12}], "actuators": [{"tag": "XS-2201", "desc": "B套过滤器出口电动阀控制方式", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀控制方式", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 7}, {"tag": "XO-2201", "desc": "B套过滤器出口电动阀开控制", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀开控制", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "XC-2201", "desc": "B套过滤器出口电动阀关控制", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀关控制", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 15}], "statusSignals": [{"tag": "ZSH-2201", "desc": "B套过滤器出口电动阀开到位检测", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀开到位检测", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 8}, {"tag": "ZSL-2201", "desc": "B套过滤器出口电动阀关到位检测", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀关到位检测", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "UIA-2201", "desc": "B套过滤器出口电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "B套过滤器出口电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 10}, {"tag": "UA-2201", "desc": "B套过滤器出口电动阀综合故障", "signalType": null, "unit": "", "range": "B套过滤器出口电动阀综合故障", "rawRowId": null, "deviceId": "2201", "bindMethod": "RULE", "sourceRow": 11}], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "0001": {"allPoints": [{"tag": "SD-0001-1", "desc": "机柜间烟感探测器报警信号", "signalType": null, "unit": "", "range": "机柜间烟感探测器报警信号", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 5}, {"tag": "SH-0001-1", "desc": "机柜间温感探测器报警信号", "signalType": null, "unit": "", "range": "机柜间温感探测器报警信号", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 6}, {"tag": "ZI-0001", "desc": "发电机的运行状态", "signalType": null, "unit": "", "range": "发电机的运行状态", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 7}], "totalPointCount": 3, "deviceId": "0001", "category": "通用设备", "aliases": ["TT-0001-1", "HT-0001-1", "SD-0001-1", "SH-0001-1", "ZI-0001", "RS-0001"], "sensors": [], "actuators": [], "statusSignals": [{"tag": "SD-0001-1", "desc": "机柜间烟感探测器报警信号", "signalType": null, "unit": "", "range": "机柜间烟感探测器报警信号", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 5}, {"tag": "SH-0001-1", "desc": "机柜间温感探测器报警信号", "signalType": null, "unit": "", "range": "机柜间温感探测器报警信号", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 6}, {"tag": "ZI-0001", "desc": "发电机的运行状态", "signalType": null, "unit": "", "range": "发电机的运行状态", "rawRowId": null, "deviceId": "0001", "bindMethod": "RULE", "sourceRow": 7}], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "PT-1203": {"allPoints": [{"tag": "PT-1203", "desc": "出站压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-1203", "bindMethod": "RULE", "sourceRow": 20}, {"tag": "PT-1203", "desc": "去工业园出站压力检测", "signalType": null, "unit": "MPa", "range": "去工业园出站压力检测", "rawRowId": null, "deviceId": "PT-1203", "bindMethod": "RULE", "sourceRow": 29}], "totalPointCount": 2, "deviceId": "PT-1203", "category": "压力变送器", "aliases": ["PT-1203"], "sensors": [{"tag": "PT-1203", "desc": "出站压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-1203", "bindMethod": "RULE", "sourceRow": 20}, {"tag": "PT-1203", "desc": "去工业园出站压力检测", "signalType": null, "unit": "MPa", "range": "去工业园出站压力检测", "rawRowId": null, "deviceId": "PT-1203", "bindMethod": "RULE", "sourceRow": 29}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "2101": {"allPoints": [{"tag": "PdT-2101", "desc": "过滤器A差压检测", "signalType": null, "unit": "kPa", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 11}, {"tag": "XS-2101", "desc": "A套过滤器出口电动阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 36}, {"tag": "XO-2101", "desc": "A套过滤器出口电动阀开控制", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀开控制", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 12}, {"tag": "XC-2101", "desc": "A套过滤器出口电动阀关控制", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀关控制", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 13}, {"tag": "ZSH-2101", "desc": "A套过滤器出口电动阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 37}, {"tag": "ZSL-2101", "desc": "A套过滤器出口电动阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 38}, {"tag": "UIA-2101", "desc": "A套过滤器出口电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "A套过滤器出口电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 5}, {"tag": "UA-2101", "desc": "A套过滤器出口电动阀综合故障", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀综合故障", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 6}], "totalPointCount": 8, "deviceId": "2101", "category": "电动阀", "aliases": ["PdT-2101", "XS-2101", "ZSH-2101", "ZSL-2101", "UIA-2101", "UA-2101", "XO-2101", "XC-2101"], "sensors": [{"tag": "PdT-2101", "desc": "过滤器A差压检测", "signalType": null, "unit": "kPa", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 11}], "actuators": [{"tag": "XS-2101", "desc": "A套过滤器出口电动阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 36}, {"tag": "XO-2101", "desc": "A套过滤器出口电动阀开控制", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀开控制", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 12}, {"tag": "XC-2101", "desc": "A套过滤器出口电动阀关控制", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀关控制", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 13}], "statusSignals": [{"tag": "ZSH-2101", "desc": "A套过滤器出口电动阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 37}, {"tag": "ZSL-2101", "desc": "A套过滤器出口电动阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 38}, {"tag": "UIA-2101", "desc": "A套过滤器出口电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "A套过滤器出口电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 5}, {"tag": "UA-2101", "desc": "A套过滤器出口电动阀综合故障", "signalType": null, "unit": "", "range": "A套过滤器出口电动阀综合故障", "rawRowId": null, "deviceId": "2101", "bindMethod": "RULE", "sourceRow": 6}], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "4202": {"allPoints": [{"tag": "XZ-4202", "desc": "调压系统B工作调节阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4202", "bindMethod": "RULE", "sourceRow": 17}, {"tag": "XC-4202", "desc": "调压系统B工作调节阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4202", "bindMethod": "RULE", "sourceRow": 26}], "totalPointCount": 2, "deviceId": "4202", "category": "阀门", "aliases": ["XZ-4202", "PT-4202", "XC-4202"], "sensors": [], "actuators": [{"tag": "XZ-4202", "desc": "调压系统B工作调节阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4202", "bindMethod": "RULE", "sourceRow": 17}, {"tag": "XC-4202", "desc": "调压系统B工作调节阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4202", "bindMethod": "RULE", "sourceRow": 26}], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "0901": {"allPoints": [], "totalPointCount": 0, "deviceId": "0901", "category": "通用设备", "aliases": ["GT-0901"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "RS-0005": {"allPoints": [{"tag": "RS-0005", "desc": "火灾报警系统型号指示", "signalType": null, "unit": "", "range": "火灾报警系统型号指示", "rawRowId": null, "deviceId": "RS-0005", "bindMethod": "RULE", "sourceRow": 51}], "totalPointCount": 1, "deviceId": "RS-0005", "category": "RS485采集器", "aliases": ["RS-0005"], "sensors": [{"tag": "RS-0005", "desc": "火灾报警系统型号指示", "signalType": null, "unit": "", "range": "火灾报警系统型号指示", "rawRowId": null, "deviceId": "RS-0005", "bindMethod": "RULE", "sourceRow": 51}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "RS-0004": {"allPoints": [{"tag": "RS-0004", "desc": "UPS综合信号指示", "signalType": null, "unit": "", "range": "UPS综合信号指示", "rawRowId": null, "deviceId": "RS-0004", "bindMethod": "RULE", "sourceRow": 50}], "totalPointCount": 1, "deviceId": "RS-0004", "category": "RS485采集器", "aliases": ["RS-0004"], "sensors": [{"tag": "RS-0004", "desc": "UPS综合信号指示", "signalType": null, "unit": "", "range": "UPS综合信号指示", "rawRowId": null, "deviceId": "RS-0004", "bindMethod": "RULE", "sourceRow": 50}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "TT-0001-1": {"allPoints": [{"tag": "TT-0001-1", "desc": "机柜间温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-0001-1", "bindMethod": "RULE", "sourceRow": 21}], "totalPointCount": 1, "deviceId": "TT-0001-1", "category": "温度变送器", "aliases": ["TT-0001-1"], "sensors": [{"tag": "TT-0001-1", "desc": "机柜间温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-0001-1", "bindMethod": "RULE", "sourceRow": 21}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "TT-1204": {"allPoints": [{"tag": "TT-1204", "desc": "出站温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-1204", "bindMethod": "RULE", "sourceRow": 19}], "totalPointCount": 1, "deviceId": "TT-1204", "category": "温度变送器", "aliases": ["TT-1204"], "sensors": [{"tag": "TT-1204", "desc": "出站温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-1204", "bindMethod": "RULE", "sourceRow": 19}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "4101": {"allPoints": [{"tag": "XZ-4101", "desc": "调压系统A监控调压阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 13}, {"tag": "XC-4101", "desc": "调压系统A监控调压阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 23}, {"tag": "XS-4101", "desc": "调压系统A监控调压阀控制方式", "signalType": null, "unit": "", "range": "调压系统A监控调压阀控制方式", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 24}, {"tag": "EXC-4101", "desc": "调压系统A安全切断阀ESD关", "signalType": null, "unit": "", "range": "调压系统A安全切断阀ESD关", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 37}, {"tag": "UA-4101", "desc": "调压系统A监控调压阀综合故障", "signalType": null, "unit": "", "range": "调压系统A监控调压阀综合故障", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 25}, {"tag": "PVXS-4101", "desc": "调压系统A工作调节阀控制方式", "signalType": null, "unit": "", "range": "调压系统A工作调节阀控制方式", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 26}, {"tag": "PVUA-4101", "desc": "调压系统A工作调节阀综合故障", "signalType": null, "unit": "", "range": "调压系统A工作调节阀综合故障", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 27}, {"tag": "ZSL-4101", "desc": "调压系统A安全切断阀关到位检测", "signalType": null, "unit": "", "range": "调压系统A安全切断阀关到位检测", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 30}], "totalPointCount": 8, "deviceId": "4101", "category": "电动阀", "aliases": ["XZ-4101", "XC-4101", "XS-4101", "UA-4101", "PVXS-4101", "PVUA-4101", "ZSL-4101", "EXC-4101"], "sensors": [], "actuators": [{"tag": "XZ-4101", "desc": "调压系统A监控调压阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 13}, {"tag": "XC-4101", "desc": "调压系统A监控调压阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 23}, {"tag": "XS-4101", "desc": "调压系统A监控调压阀控制方式", "signalType": null, "unit": "", "range": "调压系统A监控调压阀控制方式", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 24}, {"tag": "EXC-4101", "desc": "调压系统A安全切断阀ESD关", "signalType": null, "unit": "", "range": "调压系统A安全切断阀ESD关", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 37}], "statusSignals": [{"tag": "UA-4101", "desc": "调压系统A监控调压阀综合故障", "signalType": null, "unit": "", "range": "调压系统A监控调压阀综合故障", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 25}, {"tag": "PVXS-4101", "desc": "调压系统A工作调节阀控制方式", "signalType": null, "unit": "", "range": "调压系统A工作调节阀控制方式", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 26}, {"tag": "PVUA-4101", "desc": "调压系统A工作调节阀综合故障", "signalType": null, "unit": "", "range": "调压系统A工作调节阀综合故障", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 27}, {"tag": "ZSL-4101", "desc": "调压系统A安全切断阀关到位检测", "signalType": null, "unit": "", "range": "调压系统A安全切断阀关到位检测", "rawRowId": null, "deviceId": "4101", "bindMethod": "RULE", "sourceRow": 30}], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "4201": {"allPoints": [{"tag": "XZ-4201", "desc": "调压系统B监控调压阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 16}, {"tag": "XC-4201", "desc": "调压系统B监控调压阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 25}, {"tag": "XS-4201", "desc": "调压系统B监控调压阀控制方式", "signalType": null, "unit": "", "range": "调压系统B监控调压阀控制方式", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 28}, {"tag": "EXC-4201", "desc": "调压系统B安全切断阀ESD关", "signalType": null, "unit": "", "range": "调压系统B安全切断阀ESD关", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 38}, {"tag": "UA-4201", "desc": "调压系统B监控调压阀综合故障", "signalType": null, "unit": "", "range": "调压系统B监控调压阀综合故障", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 29}, {"tag": "PVXS-4201", "desc": "调压系统B工作调节阀控制方式", "signalType": null, "unit": "", "range": "调压系统B工作调节阀控制方式", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 30}, {"tag": "PVUA-4201", "desc": "调压系统B工作调节阀综合故障", "signalType": null, "unit": "", "range": "调压系统B工作调节阀综合故障", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 31}, {"tag": "ZSL-4201", "desc": "调压系统B安全切断阀关到位检测", "signalType": null, "unit": "", "range": "调压系统B安全切断阀关到位检测", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 31}], "totalPointCount": 8, "deviceId": "4201", "category": "电动阀", "aliases": ["XZ-4201", "XC-4201", "XS-4201", "UA-4201", "PVXS-4201", "PVUA-4201", "ZSL-4201", "EXC-4201"], "sensors": [], "actuators": [{"tag": "XZ-4201", "desc": "调压系统B监控调压阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 16}, {"tag": "XC-4201", "desc": "调压系统B监控调压阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 25}, {"tag": "XS-4201", "desc": "调压系统B监控调压阀控制方式", "signalType": null, "unit": "", "range": "调压系统B监控调压阀控制方式", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 28}, {"tag": "EXC-4201", "desc": "调压系统B安全切断阀ESD关", "signalType": null, "unit": "", "range": "调压系统B安全切断阀ESD关", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 38}], "statusSignals": [{"tag": "UA-4201", "desc": "调压系统B监控调压阀综合故障", "signalType": null, "unit": "", "range": "调压系统B监控调压阀综合故障", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 29}, {"tag": "PVXS-4201", "desc": "调压系统B工作调节阀控制方式", "signalType": null, "unit": "", "range": "调压系统B工作调节阀控制方式", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 30}, {"tag": "PVUA-4201", "desc": "调压系统B工作调节阀综合故障", "signalType": null, "unit": "", "range": "调压系统B工作调节阀综合故障", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 31}, {"tag": "ZSL-4201", "desc": "调压系统B安全切断阀关到位检测", "signalType": null, "unit": "", "range": "调压系统B安全切断阀关到位检测", "rawRowId": null, "deviceId": "4201", "bindMethod": "RULE", "sourceRow": 31}], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "4102": {"allPoints": [{"tag": "XZ-4102", "desc": "调压系统A工作调节阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4102", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "XC-4102", "desc": "调压系统A工作调节阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4102", "bindMethod": "RULE", "sourceRow": 24}], "totalPointCount": 2, "deviceId": "4102", "category": "阀门", "aliases": ["XZ-4102", "PT-4102", "XC-4102"], "sensors": [], "actuators": [{"tag": "XZ-4102", "desc": "调压系统A工作调节阀阀位指示", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4102", "bindMethod": "RULE", "sourceRow": 14}, {"tag": "XC-4102", "desc": "调压系统A工作调节阀阀位控制", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "4102", "bindMethod": "RULE", "sourceRow": 24}], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "GT-0901": {"allPoints": [{"tag": "GT-0901", "desc": "工艺装置区可燃气体检测", "signalType": null, "unit": "", "range": "工艺装置区可燃气体检测", "rawRowId": null, "deviceId": "GT-0901", "bindMethod": "RULE", "sourceRow": 46}], "totalPointCount": 1, "deviceId": "GT-0901", "category": "气体检测器", "aliases": ["GT-0901"], "sensors": [{"tag": "GT-0901", "desc": "工艺装置区可燃气体检测", "signalType": null, "unit": "", "range": "工艺装置区可燃气体检测", "rawRowId": null, "deviceId": "GT-0901", "bindMethod": "RULE", "sourceRow": 46}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "1106": {"allPoints": [{"tag": "XS-1106", "desc": "进站放空电动阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 30}, {"tag": "XO-1106", "desc": "进站放空电动阀开控制", "signalType": null, "unit": "", "range": "进站放空电动阀开控制", "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 10}, {"tag": "XC-1106", "desc": "进站放空电动阀关控制", "signalType": null, "unit": "", "range": "进站放空电动阀关控制", "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 11}, {"tag": "ZSH-1106", "desc": "进站放空电动阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 31}, {"tag": "ZSL-1106", "desc": "进站放空电动阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "UIA-1106", "desc": "进站放空电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "UA-1106", "desc": "进站放空电动执行机构综合故障", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 34}], "totalPointCount": 7, "deviceId": "1106", "category": "电动阀", "aliases": ["XS-1106", "ZSH-1106", "ZSL-1106", "UIA-1106", "UA-1106", "XO-1106", "XC-1106"], "sensors": [], "actuators": [{"tag": "XS-1106", "desc": "进站放空电动阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 30}, {"tag": "XO-1106", "desc": "进站放空电动阀开控制", "signalType": null, "unit": "", "range": "进站放空电动阀开控制", "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 10}, {"tag": "XC-1106", "desc": "进站放空电动阀关控制", "signalType": null, "unit": "", "range": "进站放空电动阀关控制", "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 11}], "statusSignals": [{"tag": "ZSH-1106", "desc": "进站放空电动阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 31}, {"tag": "ZSL-1106", "desc": "进站放空电动阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "UIA-1106", "desc": "进站放空电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "UA-1106", "desc": "进站放空电动执行机构综合故障", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1106", "bindMethod": "RULE", "sourceRow": 34}], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "1204": {"allPoints": [], "totalPointCount": 0, "deviceId": "1204", "category": "泵", "aliases": ["TT-1204"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "1203": {"allPoints": [], "totalPointCount": 0, "deviceId": "1203", "category": "泵", "aliases": ["PT-1203"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "1202": {"allPoints": [{"tag": "XS-1202", "desc": "出站放空电动阀控制方式", "signalType": null, "unit": "", "range": "出站放空电动阀控制方式", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 35}, {"tag": "XO-1202", "desc": "出站放空电动阀开控制", "signalType": null, "unit": "", "range": "出站放空电动阀开控制", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 23}, {"tag": "XC-1202", "desc": "出站放空电动阀关控制", "signalType": null, "unit": "", "range": "出站放空电动阀关控制", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 24}, {"tag": "ZSH-1202", "desc": "出站放空电动阀开到位检测", "signalType": null, "unit": "", "range": "出站放空电动阀开到位检测", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 36}, {"tag": "ZSL-1202", "desc": "出站放空电动阀关到位检测", "signalType": null, "unit": "", "range": "出站放空电动阀关到位检测", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 37}, {"tag": "UIA-1202", "desc": "出站放空电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "出站放空电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 38}, {"tag": "UA-1202", "desc": "出站放空电动执行机构综合故障", "signalType": null, "unit": "", "range": "出站放空电动执行机构综合故障", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 39}], "totalPointCount": 7, "deviceId": "1202", "category": "电动阀", "aliases": ["XS-1202", "ZSH-1202", "ZSL-1202", "UIA-1202", "UA-1202", "XO-1202", "XC-1202"], "sensors": [], "actuators": [{"tag": "XS-1202", "desc": "出站放空电动阀控制方式", "signalType": null, "unit": "", "range": "出站放空电动阀控制方式", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 35}, {"tag": "XO-1202", "desc": "出站放空电动阀开控制", "signalType": null, "unit": "", "range": "出站放空电动阀开控制", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 23}, {"tag": "XC-1202", "desc": "出站放空电动阀关控制", "signalType": null, "unit": "", "range": "出站放空电动阀关控制", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 24}], "statusSignals": [{"tag": "ZSH-1202", "desc": "出站放空电动阀开到位检测", "signalType": null, "unit": "", "range": "出站放空电动阀开到位检测", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 36}, {"tag": "ZSL-1202", "desc": "出站放空电动阀关到位检测", "signalType": null, "unit": "", "range": "出站放空电动阀关到位检测", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 37}, {"tag": "UIA-1202", "desc": "出站放空电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "出站放空电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 38}, {"tag": "UA-1202", "desc": "出站放空电动执行机构综合故障", "signalType": null, "unit": "", "range": "出站放空电动执行机构综合故障", "rawRowId": null, "deviceId": "1202", "bindMethod": "RULE", "sourceRow": 39}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "PT-4102": {"allPoints": [{"tag": "PT-4102", "desc": "调压系统A后压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-4102", "bindMethod": "RULE", "sourceRow": 15}], "totalPointCount": 1, "deviceId": "PT-4102", "category": "压力变送器", "aliases": ["PT-4102"], "sensors": [{"tag": "PT-4102", "desc": "调压系统A后压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-4102", "bindMethod": "RULE", "sourceRow": 15}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 388986600]}, "3104": {"allPoints": [], "totalPointCount": 0, "deviceId": "3104", "category": "加热器", "aliases": ["TT-3104"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "1201": {"allPoints": [{"tag": "XS-1201", "desc": "出站紧急切断阀控制方式", "signalType": null, "unit": "", "range": "出站紧急切断阀控制方式", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "XO-1201", "desc": "出站紧急切断阀开控制", "signalType": null, "unit": "", "range": "出站紧急切断阀开控制", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 21}, {"tag": "XC-1201", "desc": "出站紧急切断阀关控制", "signalType": null, "unit": "", "range": "出站紧急切断阀关控制", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 22}, {"tag": "EXC-1201", "desc": "去滨江出站紧急切断阀ESD关", "signalType": null, "unit": "", "range": "去滨江出站紧急切断阀ESD关", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 39}, {"tag": "ZSH-1201", "desc": "出站紧急切断阀开到位检测", "signalType": null, "unit": "", "range": "出站紧急切断阀开到位检测", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "ZSL-1201", "desc": "出站紧急切断阀关到位检测", "signalType": null, "unit": "", "range": "出站紧急切断阀关到位检测", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 34}], "totalPointCount": 6, "deviceId": "1201", "category": "电动阀", "aliases": ["XS-1201", "ZSH-1201", "ZSL-1201", "XO-1201", "XC-1201", "EXC-1201"], "sensors": [], "actuators": [{"tag": "XS-1201", "desc": "出站紧急切断阀控制方式", "signalType": null, "unit": "", "range": "出站紧急切断阀控制方式", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 32}, {"tag": "XO-1201", "desc": "出站紧急切断阀开控制", "signalType": null, "unit": "", "range": "出站紧急切断阀开控制", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 21}, {"tag": "XC-1201", "desc": "出站紧急切断阀关控制", "signalType": null, "unit": "", "range": "出站紧急切断阀关控制", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 22}, {"tag": "EXC-1201", "desc": "去滨江出站紧急切断阀ESD关", "signalType": null, "unit": "", "range": "去滨江出站紧急切断阀ESD关", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 39}], "statusSignals": [{"tag": "ZSH-1201", "desc": "出站紧急切断阀开到位检测", "signalType": null, "unit": "", "range": "出站紧急切断阀开到位检测", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 33}, {"tag": "ZSL-1201", "desc": "出站紧急切断阀关到位检测", "signalType": null, "unit": "", "range": "出站紧急切断阀关到位检测", "rawRowId": null, "deviceId": "1201", "bindMethod": "RULE", "sourceRow": 34}], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "PT-4202": {"allPoints": [{"tag": "PT-4202", "desc": "调压系统B后压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-4202", "bindMethod": "RULE", "sourceRow": 18}], "totalPointCount": 1, "deviceId": "PT-4202", "category": "压力变送器", "aliases": ["PT-4202"], "sensors": [{"tag": "PT-4202", "desc": "调压系统B后压力检测", "signalType": null, "unit": "MPa", "range": null, "rawRowId": null, "deviceId": "PT-4202", "bindMethod": "RULE", "sourceRow": 18}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "1102": {"allPoints": [], "totalPointCount": 0, "deviceId": "1102", "category": "泵", "aliases": ["PT-1102"], "sensors": [], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 385983300]}, "3103": {"allPoints": [{"tag": "HSC-3103", "desc": "电加热器远程停炉", "signalType": null, "unit": "", "range": "电加热器远程停炉", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 18}, {"tag": "XI-3103", "desc": "电加热器运行状态", "signalType": null, "unit": "", "range": "电加热器运行状态", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 17}, {"tag": "UA-3103", "desc": "电加热器综合故障报警", "signalType": null, "unit": "", "range": "电加热器综合故障报警", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 18}], "totalPointCount": 3, "deviceId": "3103", "category": "加热器", "aliases": ["XI-3103", "UA-3103", "HSC-3103"], "sensors": [], "actuators": [{"tag": "HSC-3103", "desc": "电加热器远程停炉", "signalType": null, "unit": "", "range": "电加热器远程停炉", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 18}], "statusSignals": [{"tag": "XI-3103", "desc": "电加热器运行状态", "signalType": null, "unit": "", "range": "电加热器运行状态", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 17}, {"tag": "UA-3103", "desc": "电加热器综合故障报警", "signalType": null, "unit": "", "range": "电加热器综合故障报警", "rawRowId": null, "deviceId": "3103", "bindMethod": "RULE", "sourceRow": 18}], "createdAt": [2025, 8, 4, 13, 41, 16, 390978200]}, "1101": {"allPoints": [{"tag": "YS-1101", "desc": "进滨江工业园管线通球检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 35}, {"tag": "XS-1101", "desc": "进站紧急切断阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 27}, {"tag": "XO-1101", "desc": "进站紧急切断阀开控制", "signalType": null, "unit": "", "range": "进站紧急切断阀开控制", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 8}, {"tag": "XC-1101", "desc": "进站紧急切断阀关控制", "signalType": null, "unit": "", "range": "进站紧急切断阀关控制", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "EXC-1101", "desc": "进站紧急切断阀ESD关", "signalType": null, "unit": "", "range": "进站紧急切断阀ESD关", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 36}, {"tag": "ZSH-1101", "desc": "进站紧急切断阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 28}, {"tag": "ZSL-1101", "desc": "进站紧急切断阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 29}], "totalPointCount": 7, "deviceId": "1101", "category": "电动阀", "aliases": ["TT-1101", "XS-1101", "ZSH-1101", "ZSL-1101", "YS-1101", "DS-1101", "XO-1101", "XC-1101", "PT-1101", "EXC-1101"], "sensors": [{"tag": "YS-1101", "desc": "进滨江工业园管线通球检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 35}], "actuators": [{"tag": "XS-1101", "desc": "进站紧急切断阀控制方式", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 27}, {"tag": "XO-1101", "desc": "进站紧急切断阀开控制", "signalType": null, "unit": "", "range": "进站紧急切断阀开控制", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 8}, {"tag": "XC-1101", "desc": "进站紧急切断阀关控制", "signalType": null, "unit": "", "range": "进站紧急切断阀关控制", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 9}, {"tag": "EXC-1101", "desc": "进站紧急切断阀ESD关", "signalType": null, "unit": "", "range": "进站紧急切断阀ESD关", "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 36}], "statusSignals": [{"tag": "ZSH-1101", "desc": "进站紧急切断阀开到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 28}, {"tag": "ZSL-1101", "desc": "进站紧急切断阀关到位检测", "signalType": null, "unit": "", "range": null, "rawRowId": null, "deviceId": "1101", "bindMethod": "RULE", "sourceRow": 29}], "createdAt": [2025, 8, 4, 13, 41, 16, 388119500]}, "3201": {"allPoints": [{"tag": "XS-3201", "desc": "加热器旁通电动阀控制方式", "signalType": null, "unit": "", "range": "加热器旁通电动阀控制方式", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 19}, {"tag": "XO-3201", "desc": "加热器旁通电动阀开控制", "signalType": null, "unit": "", "range": "加热器旁通电动阀开控制", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 19}, {"tag": "XC-3201", "desc": "加热器旁通电动阀关控制", "signalType": null, "unit": "", "range": "加热器旁通电动阀关控制", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 20}, {"tag": "ZSH-3201", "desc": "加热器旁通电动阀开到位检测", "signalType": null, "unit": "", "range": "加热器旁通电动阀开到位检测", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 20}, {"tag": "ZSL-3201", "desc": "加热器旁通电动阀关到位检测", "signalType": null, "unit": "", "range": "加热器旁通电动阀关到位检测", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 21}, {"tag": "UIA-3201", "desc": "旁通电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "旁通电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 22}, {"tag": "UA-3201", "desc": "加热器旁通电动阀综合故障", "signalType": null, "unit": "", "range": "加热器旁通电动阀综合故障", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 23}], "totalPointCount": 7, "deviceId": "3201", "category": "电动阀", "aliases": ["XS-3201", "ZSH-3201", "ZSL-3201", "UIA-3201", "UA-3201", "XO-3201", "XC-3201"], "sensors": [], "actuators": [{"tag": "XS-3201", "desc": "加热器旁通电动阀控制方式", "signalType": null, "unit": "", "range": "加热器旁通电动阀控制方式", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 19}, {"tag": "XO-3201", "desc": "加热器旁通电动阀开控制", "signalType": null, "unit": "", "range": "加热器旁通电动阀开控制", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 19}, {"tag": "XC-3201", "desc": "加热器旁通电动阀关控制", "signalType": null, "unit": "", "range": "加热器旁通电动阀关控制", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 20}], "statusSignals": [{"tag": "ZSH-3201", "desc": "加热器旁通电动阀开到位检测", "signalType": null, "unit": "", "range": "加热器旁通电动阀开到位检测", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 20}, {"tag": "ZSL-3201", "desc": "加热器旁通电动阀关到位检测", "signalType": null, "unit": "", "range": "加热器旁通电动阀关到位检测", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 21}, {"tag": "UIA-3201", "desc": "旁通电动执行机构过扭矩报警", "signalType": null, "unit": "", "range": "旁通电动执行机构过扭矩报警", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 22}, {"tag": "UA-3201", "desc": "加热器旁通电动阀综合故障", "signalType": null, "unit": "", "range": "加热器旁通电动阀综合故障", "rawRowId": null, "deviceId": "3201", "bindMethod": "RULE", "sourceRow": 23}], "createdAt": [2025, 8, 4, 13, 41, 16, 390978200]}, "TT-3104": {"allPoints": [{"tag": "TT-3104", "desc": "电加热器后温度检测", "signalType": null, "unit": "℃", "range": "电加热器后温度检测", "rawRowId": null, "deviceId": "TT-3104", "bindMethod": "RULE", "sourceRow": 28}], "totalPointCount": 1, "deviceId": "TT-3104", "category": "温度变送器", "aliases": ["TT-3104"], "sensors": [{"tag": "TT-3104", "desc": "电加热器后温度检测", "signalType": null, "unit": "℃", "range": "电加热器后温度检测", "rawRowId": null, "deviceId": "TT-3104", "bindMethod": "RULE", "sourceRow": 28}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 391978500]}, "HT-0001-1": {"allPoints": [{"tag": "HT-0001-1", "desc": "机柜间湿度检测", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "HT-0001-1", "bindMethod": "RULE", "sourceRow": 22}], "totalPointCount": 1, "deviceId": "HT-0001-1", "category": "湿度变送器", "aliases": ["HT-0001-1"], "sensors": [{"tag": "HT-0001-1", "desc": "机柜间湿度检测", "signalType": null, "unit": "%", "range": null, "rawRowId": null, "deviceId": "HT-0001-1", "bindMethod": "RULE", "sourceRow": 22}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 389979500]}, "RS-0001": {"allPoints": [{"tag": "RS-0001", "desc": "恒电位仪A指示", "signalType": null, "unit": "", "range": "恒电位仪A指示", "rawRowId": null, "deviceId": "RS-0001", "bindMethod": "RULE", "sourceRow": 47}], "totalPointCount": 1, "deviceId": "RS-0001", "category": "RS485采集器", "aliases": ["RS-0001"], "sensors": [{"tag": "RS-0001", "desc": "恒电位仪A指示", "signalType": null, "unit": "", "range": "恒电位仪A指示", "rawRowId": null, "deviceId": "RS-0001", "bindMethod": "RULE", "sourceRow": 47}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "TT-1101": {"allPoints": [{"tag": "TT-1101", "desc": "出站温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-1101", "bindMethod": "RULE", "sourceRow": 10}], "totalPointCount": 1, "deviceId": "TT-1101", "category": "温度变送器", "aliases": ["TT-1101"], "sensors": [{"tag": "TT-1101", "desc": "出站温度检测", "signalType": null, "unit": "℃", "range": null, "rawRowId": null, "deviceId": "TT-1101", "bindMethod": "RULE", "sourceRow": 10}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 388119500]}, "RS-0003": {"allPoints": [{"tag": "RS-0003", "desc": "低压开关柜指示", "signalType": null, "unit": "", "range": "低压开关柜指示", "rawRowId": null, "deviceId": "RS-0003", "bindMethod": "RULE", "sourceRow": 49}], "totalPointCount": 1, "deviceId": "RS-0003", "category": "RS485采集器", "aliases": ["RS-0003"], "sensors": [{"tag": "RS-0003", "desc": "低压开关柜指示", "signalType": null, "unit": "", "range": "低压开关柜指示", "rawRowId": null, "deviceId": "RS-0003", "bindMethod": "RULE", "sourceRow": 49}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}, "RS-0002": {"allPoints": [{"tag": "RS-0002", "desc": "恒电位仪B指示", "signalType": null, "unit": "", "range": "恒电位仪B指示", "rawRowId": null, "deviceId": "RS-0002", "bindMethod": "RULE", "sourceRow": 48}], "totalPointCount": 1, "deviceId": "RS-0002", "category": "RS485采集器", "aliases": ["RS-0002"], "sensors": [{"tag": "RS-0002", "desc": "恒电位仪B指示", "signalType": null, "unit": "", "range": "恒电位仪B指示", "rawRowId": null, "deviceId": "RS-0002", "bindMethod": "RULE", "sourceRow": 48}], "actuators": [], "statusSignals": [], "createdAt": [2025, 8, 4, 13, 41, 16, 392979100]}}