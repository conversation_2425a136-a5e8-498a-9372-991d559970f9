package com.example.demo.parser;

import com.example.demo.model.IoPoint;
import com.example.demo.service.EmbeddingService;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Word文档解析器
 * 用于解析.docx格式的I/O表文档
 */
public class WordDocumentParser {

    private static final Logger logger = LoggerFactory.getLogger(WordDocumentParser.class);

    private final SemanticHeaderDetector headerDetector;

    /**
     * 构造函数
     */
    public WordDocumentParser() {
        EmbeddingService embeddingService = new EmbeddingService();
        this.headerDetector = new SemanticHeaderDetector(embeddingService);
    }

    /**
     * 解析Word文档中的I/O表
     * 
     * @param filePath 文档路径
     * @return I/O点位列表
     * @throws IOException 文件读取异常
     */
    public List<IoPoint> parseDocument(String filePath) throws IOException {
        List<IoPoint> ioPoints = new ArrayList<>();

        System.out.println("开始解析Word文档: " + filePath);

        try (FileInputStream fis = new FileInputStream(filePath);
                XWPFDocument document = new XWPFDocument(fis)) {

            // 解析文档中的所有表格
            List<XWPFTable> tables = document.getTables();
            System.out.println("文档中共找到 " + tables.size() + " 个表格");

            for (int tableIndex = 0; tableIndex < tables.size(); tableIndex++) {
                XWPFTable table = tables.get(tableIndex);
                System.out.println("解析第 " + (tableIndex + 1) + " 个表格");

                List<IoPoint> tablePoints = parseTable(table, tableIndex);
                ioPoints.addAll(tablePoints);
            }

        } catch (IOException e) {
            logger.error("解析Word文档失败: {}", e.getMessage(), e);
            throw e;
        }

        System.out.println("文档解析完成，共提取 " + ioPoints.size() + " 个I/O点位");
        return ioPoints;
    }

    /**
     * 解析单个表格
     * 
     * @param table      表格对象
     * @param tableIndex 表格索引
     * @return I/O点位列表
     */
    private List<IoPoint> parseTable(XWPFTable table, int tableIndex) {
        List<IoPoint> points = new ArrayList<>();
        List<XWPFTableRow> rows = table.getRows();

        if (rows.isEmpty()) {
            logger.warn("表格 {} 为空", tableIndex);
            return points;
        }

        // 使用语义检测器查找列映射
        Map<ColumnRole, Integer> semanticMapping = headerDetector.detectHeader(table);
        if (semanticMapping == null) {
            logger.warn("表格 {} 语义表头检测失败", tableIndex);
            return points;
        }

        // 转换为传统的TableColumnMapping格式
        TableColumnMapping columnMapping = convertToTableColumnMapping(semanticMapping);
        if (columnMapping == null) {
            logger.warn("表格 {} 缺少必要的列映射", tableIndex);
            return points;
        }

        logger.info("表格 {} 列映射: {}", tableIndex, columnMapping);

        // 从数据行开始解析（跳过表头）
        for (int rowIndex = columnMapping.getHeaderRowIndex() + 1; rowIndex < rows.size(); rowIndex++) {
            XWPFTableRow row = rows.get(rowIndex);

            // 行级过滤：检查是否为元数据行或子表头行
            if (isMetaDataRow(row) || isSubHeaderRow(row)) {
                // logger.debug("跳过元数据/子表头行: {}", rowIndex + 1);
                continue;
            }

            try {
                IoPoint point = parseTableRow(row, rowIndex, columnMapping);
                if (point != null && isValidPoint(point)) {
                    points.add(point);
                    // logger.debug("解析到点位: {}", point.getTag());
                }
            } catch (Exception e) {
                logger.warn("解析第 {} 行失败: {}", rowIndex + 1, e.getMessage());
            }
        }

        System.out.println("表格 " + tableIndex + " 解析完成，提取 " + points.size() + " 个点位");
        return points;
    }

    /**
     * 将语义映射转换为传统的TableColumnMapping
     * 
     * @param semanticMapping 语义列映射
     * @return TableColumnMapping对象
     */
    private TableColumnMapping convertToTableColumnMapping(Map<ColumnRole, Integer> semanticMapping) {
        // 检查必要的列是否存在
        if (!semanticMapping.containsKey(ColumnRole.TAG) ||
                !semanticMapping.containsKey(ColumnRole.DESCRIPTION)) {
            return null;
        }

        TableColumnMapping mapping = new TableColumnMapping();
        mapping.setHeaderRowIndex(0); // 语义检测器会找到正确的表头行
        mapping.setTagColumnIndex(semanticMapping.getOrDefault(ColumnRole.TAG, -1));
        mapping.setDescColumnIndex(semanticMapping.getOrDefault(ColumnRole.DESCRIPTION, -1));
        mapping.setSignalTypeColumnIndex(semanticMapping.getOrDefault(ColumnRole.SIGNAL_TYPE, -1));
        mapping.setUnitColumnIndex(semanticMapping.getOrDefault(ColumnRole.UNIT, -1));
        mapping.setRangeColumnIndex(semanticMapping.getOrDefault(ColumnRole.SIGNAL_RANGE, -1));

        return mapping;
    }

    /**
     * 查找表格的列映射关系
     * 
     * @param rows 表格行列表
     * @return 列映射对象
     */
    private TableColumnMapping findColumnMapping(List<XWPFTableRow> rows) {
        // 扩大扫描范围，检查前10行或直到找到有效表头
        int maxScan = Math.min(10, rows.size());

        for (int i = 0; i < maxScan; i++) {
            XWPFTableRow row = rows.get(i);

            // 跳过明显的项目信息行
            if (isProjectInfoRow(row)) {
                logger.debug("跳过项目信息行: {}", i + 1);
                continue;
            }

            TableColumnMapping mapping = analyzeHeaderRow(row, i);
            if (mapping != null && mapping.isValid()) {
                // 验证映射是否正确
                if (validateMapping(mapping, rows)) {
                    logger.info("找到有效表头，行号: {}", i + 1);
                    return mapping;
                } else {
                    logger.debug("表头映射验证失败，继续查找");
                }
            }
        }
        return null;
    }

    /**
     * 分析表头行，确定列映射
     * 
     * @param row      表头行
     * @param rowIndex 行索引
     * @return 列映射对象
     */
    private TableColumnMapping analyzeHeaderRow(XWPFTableRow row, int rowIndex) {
        List<XWPFTableCell> cells = row.getTableCells();
        TableColumnMapping mapping = new TableColumnMapping();
        mapping.setHeaderRowIndex(rowIndex);

        for (int i = 0; i < cells.size(); i++) {
            String cellText = getCellText(cells.get(i)).toLowerCase().trim();

            // 根据表头文本确定列类型
            if (cellText.contains("位号") || cellText.contains("tag") || cellText.contains("仪表") ||
                    cellText.contains("编号") || cellText.contains("代号")) {
                mapping.setTagColumnIndex(i);
            } else if (cellText.contains("描述") || cellText.contains("说明") || cellText.contains("description") ||
                    cellText.contains("点位描述") || cellText.contains("功能") || cellText.contains("名称")) {
                mapping.setDescColumnIndex(i);
            } else if (cellText.contains("信号") || cellText.contains("类型") || cellText.contains("type") ||
                    cellText.contains("ai") || cellText.contains("di") || cellText.contains("ao")
                    || cellText.contains("do")) {
                mapping.setSignalTypeColumnIndex(i);
            } else if (cellText.contains("单位") || cellText.contains("unit")) {
                mapping.setUnitColumnIndex(i);
            } else if (cellText.contains("量程") || cellText.contains("范围") || cellText.contains("range")) {
                mapping.setRangeColumnIndex(i);
            }
        }

        // 检查是否找到了必要的列
        if (mapping.getTagColumnIndex() >= 0 || mapping.getDescColumnIndex() >= 0) {
            return mapping;
        }

        return null;
    }

    /**
     * 解析表格行数据
     * 
     * @param row      表格行
     * @param rowIndex 行索引
     * @param mapping  列映射
     * @return I/O点位对象
     */
    private IoPoint parseTableRow(XWPFTableRow row, int rowIndex, TableColumnMapping mapping) {
        List<XWPFTableCell> cells = row.getTableCells();
        IoPoint point = new IoPoint();
        point.setSourceRow(rowIndex + 1);

        // 提取各列数据
        if (mapping.getTagColumnIndex() >= 0 && mapping.getTagColumnIndex() < cells.size()) {
            point.setTag(getCellText(cells.get(mapping.getTagColumnIndex())));
        }

        if (mapping.getDescColumnIndex() >= 0 && mapping.getDescColumnIndex() < cells.size()) {
            point.setDesc(getCellText(cells.get(mapping.getDescColumnIndex())));
        }

        if (mapping.getSignalTypeColumnIndex() >= 0 && mapping.getSignalTypeColumnIndex() < cells.size()) {
            point.setSignalType(getCellText(cells.get(mapping.getSignalTypeColumnIndex())));
        }

        if (mapping.getUnitColumnIndex() >= 0 && mapping.getUnitColumnIndex() < cells.size()) {
            point.setUnit(getCellText(cells.get(mapping.getUnitColumnIndex())));
        }

        if (mapping.getRangeColumnIndex() >= 0 && mapping.getRangeColumnIndex() < cells.size()) {
            point.setRange(getCellText(cells.get(mapping.getRangeColumnIndex())));
        }

        return point;
    }

    /**
     * 获取单元格文本内容
     * 
     * @param cell 单元格
     * @return 文本内容
     */
    private String getCellText(XWPFTableCell cell) {
        if (cell == null) {
            return "";
        }

        StringBuilder text = new StringBuilder();
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            text.append(paragraph.getText());
        }

        return text.toString().trim();
    }

    /**
     * 验证点位数据是否有效
     *
     * @param point I/O点位
     * @return 是否有效
     */
    private boolean isValidPoint(IoPoint point) {
        String tag = point.getTag();
        String desc = point.getDesc();

        // 至少需要有位号或描述之一
        if ((tag == null || tag.trim().isEmpty()) && (desc == null || desc.trim().isEmpty())) {
            return false;
        }

        // 过滤完全空行，但保留有意义的分组标识行（如ESD、RS485等）
        if ((tag == null || tag.trim().isEmpty()) &&
                (desc == null || desc.trim().isEmpty())) {
            return false;
        }

        // 过滤分组标识行：ESD、RS485、BPCS等不是真正的I/O点位
        if (tag != null && !tag.trim().isEmpty()) {
            String tagUpper = tag.trim().toUpperCase();
            if (tagUpper.equals("ESD") || tagUpper.equals("RS485") || tagUpper.equals("BPCS") ||
                    tagUpper.equals("校对") || tagUpper.equals("审核") || tagUpper.equals("设计") ||
                    tagUpper.contains("仪表部分") || tagUpper.contains("控制部分")) {
                // 这些是分组标识行，不是真正的I/O点位
                // logger.debug("跳过分组标识行: {}", tag);
                return false;
            }
        }

        // 过滤掉明显的非点位行
        if (tag != null) {
            String tagLower = tag.toLowerCase().trim();

            // 使用配置化的忽略规则过滤测试数据和无效Tag
            if (tag.matches("^UNKNOWN_.*$|^UNKNOWN_DEVICE$") || tag.trim().isEmpty()) {
                logger.debug("跳过测试/无效Tag: {}", tag);
                return false;
            }

            // 跳过项目信息行（处理带空格的情况）
            String tagNoSpace = tagLower.replaceAll("\\s+", ""); // 去除所有空格
            if (tagLower.contains("项目") || tagLower.contains("阶段") ||
                    tagLower.contains("日期") || tagLower.contains("审核") ||
                    tagLower.contains("设计") || tagLower.contains("版本") ||
                    tagLower.contains("校对") || tagNoSpace.contains("校对") ||
                    tagNoSpace.contains("审核") || tagNoSpace.contains("设计") ||
                    tagNoSpace.contains("审定") ||
                    tagLower.length() > 30) { // 位号通常不会太长
                return false;
            }

            // 跳过文件号等元数据行
            if (tagLower.contains("文件号") || tagLower.contains("tab-") ||
                    tagLower.matches(".*文件号.*仪\\d+.*")) {
                logger.debug("跳过文件号行: {}", tag);
                return false;
            }

            // 跳过表尾注释行（但保留ESD和RS485，它们是有效的设备前缀）
            if (tagLower.contains("通信") || tagLower.contains("接口") ||
                    tagLower.contains("说明") || tagLower.contains("备注")) {
                logger.debug("跳过表尾注释行: {}", tag);
                return false;
            }
        }

        if (desc != null) {
            String descLower = desc.toLowerCase().trim();
            // 跳过表头信息
            if (descLower.contains("点位描述") || descLower.contains("仪表位号") ||
                    descLower.contains("信号类型") || descLower.equals("描述") ||
                    descLower.equals("说明") || descLower.contains("检测点名称")) {
                return false;
            }
        }

        // 检测子表头行：如果tag和desc都是表头关键词，则跳过
        if (tag != null && desc != null) {
            String tagLower = tag.toLowerCase().trim();
            String descLower = desc.toLowerCase().trim();
            if ((tagLower.equals("仪表位号") || tagLower.equals("位号")) &&
                    (descLower.contains("检测点名称") || descLower.contains("点位描述"))) {
                logger.debug("跳过子表头行: {} -> {}", tag, desc);
                return false;
            }
        }

        return true;
    }

    /**
     * 判断是否为项目信息行
     * 
     * @param row 表格行
     * @return 是否为项目信息行
     */
    private boolean isProjectInfoRow(XWPFTableRow row) {
        List<XWPFTableCell> cells = row.getTableCells();
        if (cells.isEmpty()) {
            return false;
        }

        // 检查是否包含项目信息关键词
        for (XWPFTableCell cell : cells) {
            String cellText = getCellText(cell).toLowerCase();
            if (cellText.contains("项目") || cellText.contains("阶段") ||
                    cellText.contains("日期") || cellText.contains("文件") ||
                    cellText.contains("施工图") || cellText.contains("审核") ||
                    cellText.contains("设计") || cellText.contains("版本")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证列映射是否正确
     * 
     * @param mapping 列映射
     * @param rows    表格行列表
     * @return 是否正确
     */
    private boolean validateMapping(TableColumnMapping mapping, List<XWPFTableRow> rows) {
        if (mapping.getHeaderRowIndex() + 1 >= rows.size()) {
            return false;
        }

        // 取下一行作为样本验证
        XWPFTableRow sampleRow = rows.get(mapping.getHeaderRowIndex() + 1);
        List<XWPFTableCell> cells = sampleRow.getTableCells();

        // 验证tag列是否包含设备编号格式
        if (mapping.getTagColumnIndex() >= 0 && mapping.getTagColumnIndex() < cells.size()) {
            String tagSample = getCellText(cells.get(mapping.getTagColumnIndex()));
            // 检查是否符合设备编号格式：字母-数字
            if (tagSample.matches(".*[A-Z]+-\\d+.*")) {
                return true;
            }
        }

        // 如果tag列验证失败，尝试自动修正
        return attemptAutoFix(mapping, sampleRow);
    }

    /**
     * 尝试自动修正列映射
     * 
     * @param mapping   列映射
     * @param sampleRow 样本行
     * @return 是否修正成功
     */
    private boolean attemptAutoFix(TableColumnMapping mapping, XWPFTableRow sampleRow) {
        List<XWPFTableCell> cells = sampleRow.getTableCells();

        // 尝试在所有列中找到符合设备编号格式的列
        for (int i = 0; i < cells.size(); i++) {
            String cellText = getCellText(cells.get(i));
            if (cellText.matches(".*[A-Z]+-\\d+.*")) {
                logger.info("自动修正：将tag列从 {} 调整为 {}", mapping.getTagColumnIndex(), i);

                // 如果原来的tag列现在作为desc列
                if (mapping.getTagColumnIndex() >= 0) {
                    mapping.setDescColumnIndex(mapping.getTagColumnIndex());
                }
                mapping.setTagColumnIndex(i);
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否为元数据行
     * 
     * @param row 表格行
     * @return 是否为元数据行
     */
    private boolean isMetaDataRow(XWPFTableRow row) {
        List<XWPFTableCell> cells = row.getTableCells();
        if (cells.isEmpty()) {
            return false;
        }

        // 获取整行文本
        StringBuilder rowText = new StringBuilder();
        for (XWPFTableCell cell : cells) {
            rowText.append(getCellText(cell)).append(" ");
        }
        String fullRowText = rowText.toString().toLowerCase().trim();

        // 检查是否包含元数据关键词
        return fullRowText.matches(".*文件号.*|.*项目号.*|.*阶段：.*|.*日期：.*|.*审核.*|.*设计.*");
    }

    /**
     * 判断是否为子表头行
     * 
     * @param row 表格行
     * @return 是否为子表头行
     */
    private boolean isSubHeaderRow(XWPFTableRow row) {
        List<XWPFTableCell> cells = row.getTableCells();
        if (cells.size() < 2) {
            return false;
        }

        // 获取前两列的文本
        String col1 = getCellText(cells.get(0)).toLowerCase().trim();
        String col2 = getCellText(cells.get(1)).toLowerCase().trim();

        // 检查是否为重复的表头行
        return (col1.equals("仪表位号") && col2.contains("检测点名称")) ||
                (col1.equals("位号") && col2.contains("描述")) ||
                (col1.contains("仪表位号") && col2.contains("点位描述"));
    }
}
