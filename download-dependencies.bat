@echo off
echo Downloading required JAR dependencies...

REM Create lib directory
if not exist lib mkdir lib

echo Downloading Apache POI...
curl -L -o lib/poi-5.2.4.jar "https://repo1.maven.org/maven2/org/apache/poi/poi/5.2.4/poi-5.2.4.jar"
curl -L -o lib/poi-ooxml-5.2.4.jar "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.2.4/poi-ooxml-5.2.4.jar"
curl -L -o lib/poi-ooxml-lite-5.2.4.jar "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml-lite/5.2.4/poi-ooxml-lite-5.2.4.jar"
curl -L -o lib/poi-scratchpad-5.2.4.jar "https://repo1.maven.org/maven2/org/apache/poi/poi-scratchpad/5.2.4/poi-scratchpad-5.2.4.jar"

echo Downloading Jackson...
curl -L -o lib/jackson-core-2.15.2.jar "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"
curl -L -o lib/jackson-databind-2.15.2.jar "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar"
curl -L -o lib/jackson-annotations-2.15.2.jar "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar"
curl -L -o lib/jackson-datatype-jsr310-2.15.2.jar "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.2/jackson-datatype-jsr310-2.15.2.jar"

echo Downloading SLF4J and Logback...
curl -L -o lib/slf4j-api-2.0.7.jar "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.7/slf4j-api-2.0.7.jar"
curl -L -o lib/logback-core-1.4.8.jar "https://repo1.maven.org/maven2/ch/qos/logback/logback-core/1.4.8/logback-core-1.4.8.jar"
curl -L -o lib/logback-classic-1.4.8.jar "https://repo1.maven.org/maven2/ch/qos/logback/logback-classic/1.4.8/logback-classic-1.4.8.jar"

echo Downloading Apache Commons...
curl -L -o lib/commons-lang3-3.12.0.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"
curl -L -o lib/commons-collections4-4.4.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar"

echo Downloading XML dependencies...
curl -L -o lib/xmlbeans-5.1.1.jar "https://repo1.maven.org/maven2/org/apache/xmlbeans/xmlbeans/5.1.1/xmlbeans-5.1.1.jar"
curl -L -o lib/commons-compress-1.21.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-compress/1.21/commons-compress-1.21.jar"

echo Downloading DJL dependencies...
curl -L -o lib/djl-api-0.27.0.jar "https://repo1.maven.org/maven2/ai/djl/api/0.27.0/api-0.27.0.jar"
curl -L -o lib/djl-onnxruntime-engine-0.27.0.jar "https://repo1.maven.org/maven2/ai/djl/onnxruntime/onnxruntime-engine/0.27.0/onnxruntime-engine-0.27.0.jar"
curl -L -o lib/djl-tokenizers-0.27.0.jar "https://repo1.maven.org/maven2/ai/djl/huggingface/tokenizers/0.27.0/tokenizers-0.27.0.jar"

echo All dependencies downloaded to lib/ directory
echo You can now compile with: javac -cp "lib/*;src/main/java" -d target/classes src/main/java/com/example/demo/**/*.java
