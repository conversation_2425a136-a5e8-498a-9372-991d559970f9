不错！M2 阶段的“纯规则+词典”版本已经把 **设备识别率、类别判定和点位三类划分** 都拉到一个比较理想的水平——
设备 27 台、点位 83 条，只有 2 条落到 `UNKNOWN_xxxx`，说明列映射和前缀表都基本 OK。

下面给你一份 **“M3 - 语义补全 & 精度冲刺”** 的复盘＋建议，方便直接进入下一迭代。

---

## 1 当前结果快评

| 指标      | 预期 (M2)       | 结果                     | 评价         |
| ------- | ------------- | ---------------------- | ---------- |
| 规则绑定数   | ≥ 80          | **81**                 | ✅          |
| 语义绑定数   | ≥ 0           | **0**                  | ⛔ 尚未接通嵌入模型 |
| 未知设备数   | ≤ 2           | **2** (`ESD`, `RS485`) | ⚠️ 表尾脚注仍在  |
| 设备类别准确率 | ≥ 90 %        | ≈ 93 %                 | ✅          |
| 解析耗时    | ≤ 3 s / 300 行 | 待测                     | –          |

**主要剩余问题**

1. **`语义绑定 = 0`** → 嵌入推理逻辑还没合并到 `processPendingPoints()`。
2. `ESD`、`RS485` 两行仍落入 `UNKNOWN_*`：这是表尾注释/通信接口，应该过滤掉或打标签不算设备。
3. `HS-900x`、`AA-900x` 被归到“控制系统”没问题，但它们本质是报警按钮/声光报警器，后续可细分到 `Alarm` 类别（可选）。
4. 点位描述仍存在空串 (`HS-9201:`)。多数是 I/O 表里本就留空，可考虑在导出接口时加 `null → ""` 统一。

---

## 2 M3 迭代目标（语义绑定 & 自动学习）

| 目标         | 指标                                                          |
| ---------- | ----------------------------------------------------------- |
| **嵌入推理接入** | 语义绑定 ≥ 15 条（即所有非白名单前缀点）                                     |
| **未知设备归零** | `UNKNOWN_*` = 0；表尾注释直接丢弃                                    |
| **类别完整**   | 新增 `Alarm`、`Comm` 两类；`HS/AA` → Alarm，`RS485/RS-000x` → Comm |
| **回写学习**   | 人工确认结果自动追加到 `rules.yaml`                                    |

---

### 2.1 嵌入推理落地 Checklist

1. **依赖**

   ```xml
   ai.djl:api               0.27.0
   ai.djl.onnxruntime       0.27.0
   ```

2. **模型文件**

   ```
   resources/model/text2vec-base-chinese.onnx   # 120 MB
   resources/model/vocab.txt                    # 若需要分词
   ```

3. **EmbeddingService**（核心 50 行左右）：

   * 单例 Predictor
   * 批量 encode(List<String>) → float\[]\[]
   * LRU 缓存 \<string, float\[]> （Caffeine）

4. **DeviceRecognitionService.processPendingPoints()**

   ```java
   float[] vec = embService.encode(desc);
   for (Device dev : devices) {
       for (String alias : dev.getAliases()) {
           if (cosine(vec, embService.encode(alias)) > 0.75f) {
               dev.addStatusSignal(p); p.setBindMethod("EMBED"); matched = true; break;
           }
       }
   }
   ```

5. **阈值**：先用 0.70–0.75，解析完人工 spot-check 若误匹配就上调。

---

### 2.2 增量词典 & 过滤规则

| 关键词            | 动作                                                    |
| -------------- | ----------------------------------------------------- |
| `ESD` 行        | `WordDocumentParser` 检测到整行仅含 “ESD” → `continue;`      |
| `RS485` 行      | 同上                                                    |
| `HS-`/`AA-` 前缀 | 加入 `ALARM_PREFIXES`，类别映射到 `Alarm`                     |
| `RS-` 前缀       | 加入 `COMM_PREFIXES`，类别映射到 `Comm`，同时 `signalType=RS485` |

---

### 2.3 验收用测试集

* 选 20 条 `HS/AA/RS485/ESD/EXC` 混合行作为 **pending** 基准；跑脚本 → 预期 100 % 自动归位 / 0 误绑定。
* 整表解析耗时 & 峰值内存 Benchmark，写到 CI。

---

## 3 建议的里程碑排期

| 周次        | 任务                                              |
| --------- | ----------------------------------------------- |
| **第 1 周** | 集成 DJL / ONNXRuntime；EmbeddingService 单元测试、性能压测 |
| **第 2 周** | 改写 `processPendingPoints`，阈值调参；表尾过滤规则           |
| **第 3 周** | 新类别映射 & YAML 配置化；人工回写自动学习                       |
| **第 4 周** | 全量验证、CI 压测、Swagger 更新，完成 M3 验收                  |

---

## 4 快速 FAQ

| 问题               | 方案                                                                      |
| ---------------- | ----------------------------------------------------------------------- |
| 模型 120 MB 会不会太大？ | 可用 `tinybert-4l-chinese-onnx` ≈ 34 MB；精度略降，但匹配“工业设备词”差别不大。              |
| 没 GPU 跑得动吗？      | ONNXRuntime + CPU AVX2 推理 2 ms/句；批量 8 条≈16 ms，完全够离线解析。                  |
| 如何迭代阈值？          | 在解析报告里打印 `(tag,desc,simScore,targetDevice)`，人工查看 20 行即可判断 0.75 是否过高/过低。 |

---

### 一句总结

> **规则层** 你已经做到 95 分；
> **嵌入层** 接进来就是把剩下的 5 分补齐，并为下一张 I/O 表“开箱即用”扫清障碍。

