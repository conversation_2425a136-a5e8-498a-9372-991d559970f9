package com.example.demo.service;

import com.example.demo.model.Device;
import com.example.demo.model.IoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 设备识别服务
 * 基于规则和正则表达式识别设备并绑定点位
 */
public class DeviceRecognitionService {

    private static final Logger logger = LoggerFactory.getLogger(DeviceRecognitionService.class);

    // 嵌入服务
    private final EmbeddingService embeddingService;

    // 语义相似度阈值（降低以便测试语义绑定）
    private static final double SIMILARITY_THRESHOLD = 0.60;

    // 设备编号提取正则表达式：([A-Z]+)-(\d{4})(?:-(\d+))?
    private static final Pattern DEVICE_PATTERN = Pattern.compile("([A-Z]+)-(\\d{4})(?:-(\\d+))?");

    // 描述中的设备ID抽取模式
    private static final Pattern ID_IN_DESC = Pattern.compile("(\\d{4})");

    // 传感器前缀（检测类）
    private static final Set<String> SENSOR_PREFIXES = new HashSet<>(Arrays.asList(
            "PT", "TT", "FT", "LT", "AT", "MT", "ST", "WT", "HT", "CT", "DT", "VT",
            "PI", "TI", "FI", "LI", "AI", "MI", "SI", "WI", "HI", "CI", "DI", "VI",
            "GT", "RS", "DS", "RS485"));

    // 执行器前缀（控制类）
    private static final Set<String> ACTUATOR_PREFIXES = new HashSet<>(Arrays.asList(
            "XC", "XZ", "XY", "XV", "XS", "XA", "XM", "XH", "XL", "XF", "XW", "XE", "XO",
            "AO", "DO", "MO", "SO", "HO", "CO", "FO", "LO", "TO", "WO", "VO",
            "EXC", "HSC"));

    // 状态信号前缀（指示类）
    private static final Set<String> STATUS_PREFIXES = new HashSet<>(Arrays.asList(
            "UA", "UH", "UL", "UF", "UT", "UP", "US", "UM", "UW", "UV", "UC", "UD", "UIA",
            "ZS", "ZT", "ZL", "ZH", "ZF", "ZP", "ZA", "ZM", "ZW", "ZV", "ZC", "ZD", "ZSH", "ZSL", "ZI",
            "PVXS", "PVUA", "XI", "SH", "SD", "ESD", "ES"));

    // 报警设备前缀
    private static final Set<String> ALARM_PREFIXES = new HashSet<>(Arrays.asList(
            "HS", "AA"));

    // 通信设备前缀
    private static final Set<String> COMM_PREFIXES = new HashSet<>(Arrays.asList(
            "RS"));

    // 构造函数
    public DeviceRecognitionService() {
        this.embeddingService = new EmbeddingService();
    }

    /**
     * 识别设备并绑定点位
     * 
     * @param ioPoints I/O点位列表
     * @return 设备映射表
     */
    public Map<String, Device> recognizeDevices(List<IoPoint> ioPoints) {
        System.out.println("开始设备识别，输入点位数量: " + ioPoints.size());

        Map<String, Device> deviceMap = new HashMap<>();
        List<IoPoint> pendingPoints = new ArrayList<>();

        // 第一阶段：基于规则的设备识别和点位绑定
        for (IoPoint point : ioPoints) {
            if (processPointByRule(point, deviceMap)) {
                // System.out.println("规则绑定成功: " + point.getTag() + " -> 设备 " +
                // point.getDeviceId());
            } else {
                pendingPoints.add(point);
                // System.out.println("规则绑定失败，加入待处理列表: " + point.getTag());
            }
        }

        System.out.println("规则绑定完成，成功绑定: " + (ioPoints.size() - pendingPoints.size()) +
                ", 待处理: " + pendingPoints.size());

        // 第二阶段：处理待绑定点位（基于描述的语义匹配）
        if (!pendingPoints.isEmpty()) {
            System.out.println("开始处理 " + pendingPoints.size() + " 个待绑定点位");
            logger.info("开始语义绑定，待处理点位: {}", pendingPoints.size());
        }
        processPendingPoints(pendingPoints, deviceMap);

        // 统计结果
        int totalBoundPoints = deviceMap.values().stream()
                .mapToInt(Device::getTotalPointCount)
                .sum();

        System.out.println("设备识别完成，识别设备数: " + deviceMap.size() +
                ", 绑定点位数: " + totalBoundPoints +
                ", 未绑定点位数: " + (ioPoints.size() - totalBoundPoints));

        return deviceMap;
    }

    /**
     * 基于规则处理单个点位
     * 
     * @param point     I/O点位
     * @param deviceMap 设备映射表
     * @return 是否成功绑定
     */
    private boolean processPointByRule(IoPoint point, Map<String, Device> deviceMap) {
        String tag = point.getTag();
        if (tag == null || tag.trim().isEmpty()) {
            return false;
        }

        String tagUpper = tag.trim().toUpperCase();

        // 分组标识行处理：ESD、RS485等
        if ("GROUP_HEADER".equals(point.getSignalType())) {
            Device groupDevice = deviceMap.computeIfAbsent("GROUP_" + tagUpper, id -> {
                Device device = new Device();
                device.setDeviceId("GROUP_" + tagUpper);
                device.setCategory(getGroupCategory(tagUpper));
                device.addAlias(tag);
                return device;
            });
            point.setBindMethod("RULE");
            groupDevice.addStatusSignal(point);
            logger.debug("分组标识行绑定: {} -> {}设备", tag, groupDevice.getCategory());
            return true;
        }

        // RS485专用处理：归入通信设备
        if (tagUpper.startsWith("RS485") || tagUpper.equals("RS485")) {
            Device commDevice = deviceMap.computeIfAbsent("COMM", id -> {
                Device device = new Device();
                device.setDeviceId("COMM");
                device.setCategory("通信设备");
                device.addAlias("RS485");
                return device;
            });
            point.setSignalType("RS485");
            point.setBindMethod("RULE");
            commDevice.addSensor(point);
            logger.debug("RS485通信点位绑定: {} -> COMM设备", tag);
            return true;
        }

        // 使用正则表达式提取设备信息
        Matcher matcher = DEVICE_PATTERN.matcher(tagUpper);
        if (!matcher.matches()) {
            // System.out.println("位号格式不匹配: " + tag);
            return false;
        }

        String prefix = matcher.group(1); // 前缀，如 XZ
        String deviceId = matcher.group(2); // 设备ID，如 4101
        String subId = matcher.group(3); // 子ID，如 01（可选）

        // 获取或创建设备
        Device device = deviceMap.computeIfAbsent(deviceId, this::createDevice);

        // 添加别名
        device.addAlias(tag);

        // 设置点位的设备ID和绑定方法
        point.setDeviceId(deviceId);
        point.setBindMethod("RULE");

        // 根据前缀分类点位
        boolean success = false;
        if (SENSOR_PREFIXES.contains(prefix)) {
            // 传感器点位创建独立的传感器设备
            success = createSensorDevice(point, deviceMap, prefix, deviceId);
        } else if (ACTUATOR_PREFIXES.contains(prefix)) {
            device.addActuator(point);
            success = true;
        } else if (STATUS_PREFIXES.contains(prefix)) {
            device.addStatusSignal(point);
            success = true;
        } else if (ALARM_PREFIXES.contains(prefix)) {
            // 报警设备特殊处理
            device.setCategory("报警装置");
            device.addStatusSignal(point);
            success = true;
        } else if (COMM_PREFIXES.contains(prefix)) {
            // 通信设备特殊处理
            device.setCategory("通信设备");
            point.setSignalType("RS485");
            device.addSensor(point);
            success = true;
        }

        // 如果前缀不在已知列表中，尝试根据描述判断
        if (!success) {
            success = classifyByDescription(point, device);
        }

        // 如果成功绑定，优化设备类别
        if (success) {
            optimizeDeviceCategory(device);
        }

        return success;
    }

    /**
     * 根据描述分类点位
     * 
     * @param point  I/O点位
     * @param device 设备对象
     * @return 是否成功分类
     */
    private boolean classifyByDescription(IoPoint point, Device device) {
        String desc = point.getDesc();
        if (desc == null) {
            return false;
        }

        String lowerDesc = desc.toLowerCase();

        // 传感器关键词
        if (lowerDesc.contains("检测") || lowerDesc.contains("测量") || lowerDesc.contains("监测") ||
                lowerDesc.contains("压力") || lowerDesc.contains("温度") || lowerDesc.contains("流量") ||
                lowerDesc.contains("液位") || lowerDesc.contains("sensor") || lowerDesc.contains("detect")) {
            device.addSensor(point);
            return true;
        }

        // 执行器关键词
        if (lowerDesc.contains("控制") || lowerDesc.contains("调节") || lowerDesc.contains("开关") ||
                lowerDesc.contains("启动") || lowerDesc.contains("停止") || lowerDesc.contains("阀位") ||
                lowerDesc.contains("control") || lowerDesc.contains("actuator")) {
            device.addActuator(point);
            return true;
        }

        // 状态信号关键词
        if (lowerDesc.contains("状态") || lowerDesc.contains("故障") || lowerDesc.contains("报警") ||
                lowerDesc.contains("指示") || lowerDesc.contains("反馈") || lowerDesc.contains("位置") ||
                lowerDesc.contains("status") || lowerDesc.contains("alarm") || lowerDesc.contains("fault")) {
            device.addStatusSignal(point);
            return true;
        }

        // 默认归类为状态信号
        device.addStatusSignal(point);
        return true;
    }

    /**
     * 处理待绑定点位
     * 
     * @param pendingPoints 待绑定点位列表
     * @param deviceMap     设备映射表
     */
    private void processPendingPoints(List<IoPoint> pendingPoints, Map<String, Device> deviceMap) {
        System.out.println("开始处理 " + pendingPoints.size() + " 个待绑定点位");

        for (IoPoint point : pendingPoints) {
            // 尝试基于描述的模糊匹配
            Device matchedDevice = findDeviceByDescription(point, deviceMap);
            if (matchedDevice != null) {
                point.setDeviceId(matchedDevice.getDeviceId());
                point.setBindMethod("EMBED");
                classifyByDescription(point, matchedDevice);
                // 语义绑定成功后优化设备类别
                optimizeDeviceCategory(matchedDevice);
                // System.out.println("语义绑定成功: " + point.getTag() + " -> 设备 " +
                // matchedDevice.getDeviceId());
            } else {
                // 创建新的未知设备
                String unknownDeviceId = "UNKNOWN_" + System.currentTimeMillis() % 10000;
                Device unknownDevice = createDevice(unknownDeviceId);
                unknownDevice.setCategory("未知设备");
                point.setDeviceId(unknownDeviceId);
                point.setBindMethod("MANUAL");
                unknownDevice.addStatusSignal(point);
                deviceMap.put(unknownDeviceId, unknownDevice);
                // System.out.println("创建未知设备: " + point.getTag() + " -> 设备 " +
                // unknownDeviceId);
            }
        }
    }

    /**
     * 根据描述查找匹配的设备
     * 
     * @param point     I/O点位
     * @param deviceMap 设备映射表
     * @return 匹配的设备，如果没有找到则返回null
     */
    private Device findDeviceByDescription(IoPoint point, Map<String, Device> deviceMap) {
        String desc = point.getDesc();
        if (desc == null || desc.trim().isEmpty()) {
            return null;
        }

        Device bestMatch = null;
        double bestSimilarity = 0.0;

        // 使用语义相似度匹配
        for (Device device : deviceMap.values()) {
            if (device.getDeviceId().startsWith("UNKNOWN_")) {
                continue; // 跳过未知设备
            }

            // 1. 检查设备别名的语义相似度
            for (String alias : device.getAliases()) {
                double similarity = embeddingService.calculateSimilarity(desc, alias);
                if (similarity > bestSimilarity && similarity >= SIMILARITY_THRESHOLD) {
                    bestSimilarity = similarity;
                    bestMatch = device;
                }
            }

            // 2. 检查设备类别的语义相似度
            if (device.getCategory() != null) {
                double similarity = embeddingService.calculateSimilarity(desc, device.getCategory());
                if (similarity > bestSimilarity && similarity >= SIMILARITY_THRESHOLD) {
                    bestSimilarity = similarity;
                    bestMatch = device;
                }
            }

            // 3. 检查已有点位描述的相似度
            for (IoPoint existingPoint : device.getAllPoints()) {
                if (existingPoint.getDesc() != null) {
                    double similarity = embeddingService.calculateSimilarity(desc, existingPoint.getDesc());
                    if (similarity > bestSimilarity && similarity >= SIMILARITY_THRESHOLD) {
                        bestSimilarity = similarity;
                        bestMatch = device;
                    }
                }
            }
        }

        if (bestMatch != null) {
            logger.info("语义匹配成功: {} -> 设备 {} (相似度: {:.3f})",
                    desc, bestMatch.getDeviceId(), bestSimilarity);
        } else {
            // 尝试从描述中抽取设备ID进行匹配
            bestMatch = tryExtractDeviceIdFromDescription(point, deviceMap);
            if (bestMatch != null) {
                logger.info("数字抽取匹配成功: {} -> 设备 {}", desc, bestMatch.getDeviceId());
            } else {
                // 记录未匹配的描述，用于调试和阈值调整
                logger.debug("语义匹配失败: {} (最高相似度: {:.3f})", desc, bestSimilarity);
            }
        }

        return bestMatch;
    }

    /**
     * 创建新设备
     * 
     * @param deviceId 设备ID
     * @return 设备对象
     */
    private Device createDevice(String deviceId) {
        Device device = new Device(deviceId);

        // 根据设备ID推断设备类别
        String category = inferDeviceCategory(deviceId);
        device.setCategory(category);

        // System.out.println("创建新设备: " + deviceId + " (类别: " + category + ")");
        return device;
    }

    /**
     * 根据设备ID推断设备类别
     *
     * @param deviceId 设备ID
     * @return 设备类别
     */
    private String inferDeviceCategory(String deviceId) {
        try {
            // 数据驱动的设备类别映射
            int deviceNum = Integer.parseInt(deviceId);
            int category = deviceNum / 1000;

            switch (category) {
                case 0:
                    return "通用设备"; // 000x
                case 1:
                    return "泵"; // 1xxx
                case 2:
                    return "过滤器"; // 2xxx
                case 3:
                    return "加热器"; // 3xxx
                case 4:
                    return "阀门"; // 4xxx
                case 5:
                    return "换热器"; // 5xxx
                case 6:
                    return "分离器"; // 6xxx
                case 7:
                    return "储罐"; // 7xxx
                case 8:
                    return "报警装置"; // 8xxx
                case 9:
                    if (deviceNum >= 9200) {
                        return "安全关断"; // 920x
                    } else if (deviceNum >= 9000 && deviceNum < 9100) {
                        return "报警装置"; // 900x 报警按钮/声光报警
                    } else {
                        return "控制系统"; // 其他9xxx
                    }
                default:
                    return "其他设备";
            }
        } catch (NumberFormatException e) {
            // 如果不是纯数字，尝试其他规则
            if (deviceId.contains("ESD")) {
                return "安全关断";
            } else if (deviceId.contains("PLC")) {
                return "控制系统";
            }
            return "未知类型";
        }
    }

    /**
     * 根据设备的点位特征智能优化设备类别
     * 这个方法会在设备添加点位后调用，基于实际的点位功能来修正设备类别
     *
     * @param device 设备对象
     */
    private void optimizeDeviceCategory(Device device) {
        List<IoPoint> allPoints = device.getAllPoints();
        if (allPoints.isEmpty()) {
            return;
        }

        // 统计不同类型的点位特征
        Map<String, Integer> prefixCount = new HashMap<>();
        List<String> descriptions = new ArrayList<>();

        for (IoPoint point : allPoints) {
            // 统计仪表前缀
            String tag = point.getTag();
            if (tag != null && tag.contains("-")) {
                String prefix = tag.split("-")[0];
                prefixCount.merge(prefix, 1, Integer::sum);
            }

            // 收集描述信息
            if (point.getDesc() != null && !point.getDesc().trim().isEmpty()) {
                descriptions.add(point.getDesc().toLowerCase());
            }
        }

        // 基于点位特征判断真实的设备类别
        String optimizedCategory = determineOptimizedCategory(prefixCount, descriptions, device.getCategory());

        if (!optimizedCategory.equals(device.getCategory())) {
            // System.out.println("设备 " + device.getId() + " 类别优化: " + device.getCategory()
            // + " -> " + optimizedCategory);
            device.setCategory(optimizedCategory);
        }
    }

    /**
     * 基于点位特征确定优化后的设备类别
     */
    private String determineOptimizedCategory(Map<String, Integer> prefixCount, List<String> descriptions,
            String originalCategory) {
        // 1. 检查是否为电动阀特征（优先级最高，因为特征最明显）
        if (hasValveCharacteristics(prefixCount)) {
            return "电动阀";
        }

        // 2. 检查是否为报警装置特征
        if (hasAlarmCharacteristics(prefixCount)) {
            return "报警装置";
        }

        // 3. 基于描述检查是否为特定设备类型
        String categoryByDescription = getCategoryByDescription(descriptions);
        if (categoryByDescription != null) {
            return categoryByDescription;
        }

        // 4. 检查是否为纯传感器设备（只有传感器点位，没有控制功能）
        if (isPureSensorDevice(prefixCount)) {
            return "传感器设备";
        }

        // 5. 如果没有明确特征，保持原分类
        return originalCategory;
    }

    /**
     * 检查是否具有电动阀特征
     */
    private boolean hasValveCharacteristics(Map<String, Integer> prefixCount) {
        // 电动阀的典型信号：XS(控制方式) + XO(开控制) + XC(关控制) + ZSH(开到位) + ZSL(关到位) + UA(故障)
        int valveSignals = 0;
        valveSignals += prefixCount.getOrDefault("XS", 0); // 控制方式
        valveSignals += prefixCount.getOrDefault("XO", 0); // 开控制
        valveSignals += prefixCount.getOrDefault("XC", 0); // 关控制
        valveSignals += prefixCount.getOrDefault("ZSH", 0); // 开到位
        valveSignals += prefixCount.getOrDefault("ZSL", 0); // 关到位
        valveSignals += prefixCount.getOrDefault("UIA", 0); // 过扭矩报警
        valveSignals += prefixCount.getOrDefault("UA", 0); // 综合故障

        // 如果有3个或以上的电动阀特征信号，认为是电动阀
        return valveSignals >= 3;
    }

    /**
     * 检查是否具有报警装置特征
     */
    private boolean hasAlarmCharacteristics(Map<String, Integer> prefixCount) {
        return prefixCount.containsKey("HS") || prefixCount.containsKey("AA");
    }

    /**
     * 基于描述判断设备类别
     */
    private String getCategoryByDescription(List<String> descriptions) {
        for (String desc : descriptions) {
            if (desc.contains("加热器") || desc.contains("电加热") || desc.contains("heater")) {
                return "加热器";
            }
            if (desc.contains("过滤器") || desc.contains("filter")) {
                return "过滤器";
            }
            if (desc.contains("泵") || desc.contains("pump")) {
                return "泵";
            }
            if (desc.contains("换热器") || desc.contains("heat exchanger")) {
                return "换热器";
            }
            if (desc.contains("分离器") || desc.contains("separator")) {
                return "分离器";
            }
            if (desc.contains("储罐") || desc.contains("tank")) {
                return "储罐";
            }
        }
        return null;
    }

    /**
     * 检查是否为纯传感器设备（只有传感器功能，没有控制功能）
     */
    private boolean isPureSensorDevice(Map<String, Integer> prefixCount) {
        // 检查是否有控制类前缀
        String[] controlPrefixes = { "XS", "XO", "XC", "XZ", "EXC", "HSC" };
        for (String prefix : controlPrefixes) {
            if (prefixCount.containsKey(prefix)) {
                return false; // 有控制功能，不是纯传感器设备
            }
        }

        // 检查是否有传感器前缀
        String[] sensorPrefixes = { "PT", "TT", "FT", "LT", "GT", "RS", "DS", "HT" };
        for (String prefix : sensorPrefixes) {
            if (prefixCount.containsKey(prefix)) {
                return true; // 只有传感器功能
            }
        }

        return false;
    }

    /**
     * 尝试从描述中抽取设备ID进行匹配
     * 
     * @param point     I/O点位
     * @param deviceMap 设备映射表
     * @return 匹配的设备，如果没有找到则返回null
     */
    private Device tryExtractDeviceIdFromDescription(IoPoint point, Map<String, Device> deviceMap) {
        String desc = point.getDesc();
        if (desc == null || desc.trim().isEmpty()) {
            return null;
        }

        // 从描述中抽取4位数字作为设备ID
        Matcher matcher = ID_IN_DESC.matcher(desc);
        if (matcher.find()) {
            String deviceId = matcher.group(1);
            Device targetDevice = deviceMap.get(deviceId);

            if (targetDevice != null) {
                // 计算描述与设备已有点位的相似度
                double similarity = calculateDeviceDescriptionSimilarity(desc, targetDevice);

                // 降低阈值到0.60，提高匹配成功率
                if (similarity >= 0.60) {
                    logger.debug("数字抽取+相似度验证: {} -> 设备{} (相似度: {:.3f})",
                            desc, deviceId, similarity);
                    return targetDevice;
                }
            }
        }

        return null;
    }

    /**
     * 计算描述与设备的相似度
     * 
     * @param desc   点位描述
     * @param device 设备
     * @return 相似度分数
     */
    private double calculateDeviceDescriptionSimilarity(String desc, Device device) {
        double maxSimilarity = 0.0;

        // 与设备类别的相似度
        if (device.getCategory() != null) {
            double similarity = embeddingService.calculateSimilarity(desc, device.getCategory());
            maxSimilarity = Math.max(maxSimilarity, similarity);
        }

        // 与设备已有点位描述的相似度
        for (IoPoint existingPoint : device.getAllPoints()) {
            if (existingPoint.getDesc() != null && !existingPoint.getDesc().trim().isEmpty()) {
                double similarity = embeddingService.calculateSimilarity(desc, existingPoint.getDesc());
                maxSimilarity = Math.max(maxSimilarity, similarity);
            }
        }

        return maxSimilarity;
    }

    /**
     * 获取分组类别
     * 
     * @param groupTag 分组标识
     * @return 分组类别
     */
    private String getGroupCategory(String groupTag) {
        switch (groupTag.toUpperCase()) {
            case "ESD":
                return "安全关断系统";
            case "RS485":
                return "通信系统";
            case "仪表部分":
                return "仪表系统";
            case "控制部分":
                return "控制系统";
            default:
                return "系统分组";
        }
    }

    /**
     * 为传感器点位创建独立的传感器设备
     *
     * @param point     传感器点位
     * @param deviceMap 设备映射表
     * @param prefix    仪表前缀
     * @param deviceId  原设备ID
     * @return 是否成功创建
     */
    private boolean createSensorDevice(IoPoint point, Map<String, Device> deviceMap, String prefix, String deviceId) {
        // 为传感器点位创建独立的设备ID（使用完整的Tag作为设备ID）
        String sensorDeviceId = point.getTag();

        // 获取或创建传感器设备
        Device sensorDevice = deviceMap.computeIfAbsent(sensorDeviceId, id -> {
            Device device = new Device();
            device.setDeviceId(sensorDeviceId);
            device.setCategory(getSensorDeviceCategory(prefix, point.getDesc()));
            device.addAlias(point.getTag());
            return device;
        });

        // 设置点位的设备ID和绑定方法
        point.setDeviceId(sensorDeviceId);
        point.setBindMethod("RULE");

        // 添加到传感器设备
        sensorDevice.addSensor(point);

        return true;
    }

    /**
     * 根据传感器前缀直接确定设备类别（按照rules.yaml中的定义）
     *
     * @param prefix 仪表前缀
     * @param desc   点位描述
     * @return 设备类别
     */
    private String getSensorDeviceCategory(String prefix, String desc) {
        // 直接基于前缀分类，对应rules.yaml中的instrument_prefixes
        switch (prefix) {
            // 压力类
            case "PT":
                return "压力变送器";
            case "PI":
                return "压力指示器";

            // 温度类
            case "TT":
                return "温度变送器";
            case "TI":
                return "温度指示器";

            // 流量类
            case "FT":
                return "流量变送器";
            case "FI":
                return "流量指示器";
            case "FQI":
                return "流量累积指示器";

            // 液位类
            case "LT":
                return "液位变送器";
            case "LI":
                return "液位指示器";

            // 分析类
            case "AT":
                return "分析变送器";
            case "AI":
                return "分析指示器";

            // 湿度类
            case "MT":
            case "HT":
                return "湿度变送器";
            case "MI":
            case "HI":
                return "湿度指示器";

            // 其他变送器
            case "ST":
                return "速度变送器";
            case "WT":
                return "重量变送器";
            case "CT":
                return "电导率变送器";
            case "DT":
                return "密度变送器";
            case "VT":
                return "振动变送器";

            // 其他指示器
            case "SI":
                return "速度指示器";
            case "WI":
                return "重量指示器";
            case "CI":
                return "电导率指示器";
            case "DI":
                return "密度指示器";
            case "VI":
                return "振动指示器";

            // 专用检测器
            case "GT":
                return "气体检测器";
            case "DS":
                return "差压开关";
            case "YS":
                return "通球检测器";
            case "RS":
                return "RS485采集器";

            // 差压检测
            case "PdT":
                return "差压变送器";

            default:
                return "传感器设备";
        }
    }
}
