package com.example.demo;

/**
 * App类的简单测试
 */
public class AppTest {

    public static void main(String[] args) {
        AppTest test = new AppTest();
        test.runAllTests();
    }

    public void runAllTests() {
        System.out.println("开始运行App测试...");

        testAppCreation();
        testRun();

        System.out.println("所有App测试通过！");
    }

    public void testAppCreation() {
        App app = new App();
        if (app == null) {
            throw new RuntimeException("App实例不应为null");
        }
        System.out.println("✓ testAppCreation 通过");
    }

    public void testRun() {
        App app = new App();
        try {
            app.run();
            System.out.println("✓ testRun 通过");
        } catch (Exception e) {
            throw new RuntimeException("run方法不应抛出异常", e);
        }
    }
}
