# 设计院资料识别系统 API 文档

## 项目概述

本系统提供统一的REST接口，支持上传不同类型的工程文件（Word、PDF、Excel），自动解析其中的设备和IO点位信息。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 前端      │ => │  REST API       │ => │  文件解析服务    │
│   (上传界面)    │    │  (Spring Boot)  │    │  (多格式支持)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  设备识别服务    │
                       │  (智能分组)     │
                       └─────────────────┘
```

## 支持的文件格式

- **Word文档**: `.docx`, `.doc`
- **PDF文档**: `.pdf`  
- **Excel文档**: `.xlsx`, `.xls` (待实现)

## API 接口说明

### 1. 文件上传解析 

**POST** `/api/parsing/upload`

上传文件并自动解析其中的设备和IO点位信息。

**请求参数:**
- `file`: 上传的文件 (multipart/form-data)

**响应示例:**
```json
{
  "success": true,
  "message": "文件解析成功",
  "fileType": "WORD",
  "ioPointsCount": 25,
  "devicesCount": 8,
  "ioPoints": [
    {
      "tag": "PT-1001",
      "desc": "进料压力检测",
      "signalType": "AI",
      "unit": "4-20mA",
      "range": "0-2.5MPa",
      "sourceRow": 3
    }
  ],
  "devices": [
    {
      "deviceId": "PT-1001",
      "deviceName": "压力变送器",
      "deviceType": "SENSOR",
      "ioPoints": [...],
      "createdAt": "2025-08-04T10:30:00"
    }
  ]
}
```

### 2. 本地文件解析

**POST** `/api/parsing/parse-local`

解析服务器本地的文件。

**请求体:**
```json
{
  "filePath": "C:/path/to/your/file.docx"
}
```

**响应格式:** 与上传解析接口相同

### 3. 批量解析测试文件

**POST** `/api/parsing/parse-test-files`

批量解析test_files目录下的所有支持格式文件。

**响应示例:**
```json
{
  "success": true,
  "message": "批量解析完成",
  "totalFiles": 5,
  "successFiles": 4,
  "results": {
    "7 滨江调压站IO表.docx": {
      "success": true,
      "fileType": "WORD",
      "ioPointsCount": 35,
      "devicesCount": 12,
      "ioPoints": [...],
      "devices": [...]
    },
    "BD1947D-BV01-IS01-OT01 IO表.pdf": {
      "success": true,
      "fileType": "PDF",
      "ioPointsCount": 28,
      "devicesCount": 9,
      "ioPoints": [...],
      "devices": [...]
    }
  }
}
```

### 4. 获取支持的文件类型

**GET** `/api/parsing/supported-types`

获取系统支持的文件格式列表。

**响应示例:**
```json
{
  "success": true,
  "supportedTypes": [".docx", ".doc", ".pdf", ".xlsx", ".xls"]
}
```

### 5. 健康检查

**GET** `/api/parsing/health`

检查服务运行状态。

**响应示例:**
```json
{
  "status": "healthy",
  "service": "file-parsing-service", 
  "timestamp": 1723035600000
}
```

## 数据模型

### IoPoint (IO点位)
```json
{
  "tag": "设备位号",
  "desc": "设备描述",
  "signalType": "信号类型(AI/AO/DI/DO)",
  "unit": "信号单位",
  "range": "测量范围",
  "sourceRow": "源文件行号"
}
```

### Device (设备)
```json
{
  "deviceId": "设备ID",
  "deviceName": "设备名称", 
  "deviceType": "设备类型(SENSOR/ACTUATOR/STATUS/ALARM/COMM)",
  "ioPoints": "关联的IO点位列表",
  "createdAt": "创建时间"
}
```

## 启动说明

1. **环境要求:**
   - JDK 1.8+
   - Maven 3.6+

2. **启动命令:**
   ```bash
   mvn spring-boot:run
   ```

3. **访问地址:**
   - 服务地址: http://localhost:8080
   - 健康检查: http://localhost:8080/api/parsing/health

## 使用示例

### curl 命令示例

1. **上传文件解析:**
```bash
curl -X POST -F "file=@test_files/7\ 滨江调压站IO表.docx" \
     http://localhost:8080/api/parsing/upload
```

2. **解析本地文件:**
```bash
curl -X POST -H "Content-Type: application/json" \
     -d '{"filePath":"test_files/BD1947D-BV01-IS01-OT01 IO表.pdf"}' \
     http://localhost:8080/api/parsing/parse-local
```

3. **批量解析测试文件:**
```bash
curl -X POST http://localhost:8080/api/parsing/parse-test-files
```

## 错误处理

系统采用统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述信息"
}
```

常见错误类型：
- `400 Bad Request`: 文件格式不支持、文件为空等
- `500 Internal Server Error`: 服务器内部错误、文件解析失败等

## 技术特性

1. **智能表头识别**: 使用语义分析自动识别表格列结构
2. **多格式支持**: 统一接口处理Word、PDF、Excel文件
3. **设备智能分组**: 根据设备编号规则自动分类设备
4. **容错处理**: 对格式不规范的文件进行智能修正
5. **批量处理**: 支持批量文件解析和结果对比

## 下一步开发计划

1. 完善PDF解析功能（集成PDFBox库）
2. 实现Excel文件解析支持
3. 添加文件解析历史记录功能
4. 开发Web前端界面
5. 支持更多工程文件格式