package com.example.demo.controller;

import com.example.demo.model.Device;
import com.example.demo.model.IoPoint;
import com.example.demo.service.FileParsingService;
import com.example.demo.service.FileParsingService.ParseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件解析REST控制器
 * 提供文件上传和解析的Web接口
 */
@RestController
@RequestMapping("/api/parsing")
@CrossOrigin(origins = "*")
public class FileParsingController {

    private static final Logger logger = LoggerFactory.getLogger(FileParsingController.class);

    @Autowired
    private FileParsingService fileParsingService;

    // 文件上传目录
    private static final String UPLOAD_DIR = "uploads";

    /**
     * 上传并解析文件
     * 
     * @param file 上传的文件
     * @return 解析结果
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadAndParseFile(
            @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isSupportedFileType(originalFilename)) {
                response.put("success", false);
                response.put("message", "不支持的文件类型，支持的格式：.docx, .doc, .pdf, .xlsx, .xls");
                return ResponseEntity.badRequest().body(response);
            }

            // 保存上传的文件
            String savedFilePath = saveUploadedFile(file);
            
            // 解析文件
            ParseResult result = fileParsingService.parseFile(savedFilePath);
            
            // 构建响应
            response.put("success", result.isSuccess());
            if (result.isSuccess()) {
                response.put("message", "文件解析成功");
                response.put("fileType", result.getFileType());
                response.put("ioPointsCount", result.getIoPoints().size());
                response.put("devicesCount", result.getDevices().size());
                response.put("ioPoints", result.getIoPoints());
                response.put("devices", result.getDevices());
            } else {
                response.put("message", "文件解析失败: " + result.getErrorMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("文件上传解析失败", e);
            response.put("success", false);
            response.put("message", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 解析本地文件
     * 
     * @param filePath 文件路径
     * @return 解析结果
     */
    @PostMapping("/parse-local")
    public ResponseEntity<Map<String, Object>> parseLocalFile(
            @RequestBody Map<String, String> request) {
        
        Map<String, Object> response = new HashMap<>();
        String filePath = request.get("filePath");
        
        if (filePath == null || filePath.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "文件路径不能为空");
            return ResponseEntity.badRequest().body(response);
        }
        
        try {
            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                response.put("success", false);
                response.put("message", "文件不存在: " + filePath);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 解析文件
            ParseResult result = fileParsingService.parseFile(filePath);
            
            // 构建响应
            response.put("success", result.isSuccess());
            if (result.isSuccess()) {
                response.put("message", "文件解析成功");
                response.put("fileType", result.getFileType());
                response.put("ioPointsCount", result.getIoPoints().size());
                response.put("devicesCount", result.getDevices().size());
                response.put("ioPoints", result.getIoPoints());
                response.put("devices", result.getDevices());
            } else {
                response.put("message", "文件解析失败: " + result.getErrorMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("本地文件解析失败", e);
            response.put("success", false);
            response.put("message", "解析失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量解析测试文件
     * 
     * @return 批量解析结果
     */
    @PostMapping("/parse-test-files")
    public ResponseEntity<Map<String, Object>> parseTestFiles() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 测试文件目录
            String testDir = "test_files";
            File testDirFile = new File(testDir);
            
            if (!testDirFile.exists() || !testDirFile.isDirectory()) {
                response.put("success", false);
                response.put("message", "测试文件目录不存在: " + testDir);
                return ResponseEntity.badRequest().body(response);
            }
            
            // 获取测试文件列表
            File[] testFiles = testDirFile.listFiles();
            if (testFiles == null || testFiles.length == 0) {
                response.put("success", false);
                response.put("message", "测试文件目录为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 解析每个文件
            Map<String, Object> results = new HashMap<>();
            int successCount = 0;
            int totalCount = 0;
            
            for (File file : testFiles) {
                if (file.isFile() && isSupportedFileType(file.getName())) {
                    totalCount++;
                    String filePath = file.getAbsolutePath();
                    
                    try {
                        ParseResult result = fileParsingService.parseFile(filePath);
                        Map<String, Object> fileResult = new HashMap<>();
                        fileResult.put("success", result.isSuccess());
                        fileResult.put("fileType", result.getFileType());
                        
                        if (result.isSuccess()) {
                            successCount++;
                            fileResult.put("ioPointsCount", result.getIoPoints().size());
                            fileResult.put("devicesCount", result.getDevices().size());
                            fileResult.put("ioPoints", result.getIoPoints());
                            fileResult.put("devices", result.getDevices());
                        } else {
                            fileResult.put("error", result.getErrorMessage());
                        }
                        
                        results.put(file.getName(), fileResult);
                        
                    } catch (Exception e) {
                        logger.error("解析测试文件失败: {}", file.getName(), e);
                        Map<String, Object> fileResult = new HashMap<>();
                        fileResult.put("success", false);
                        fileResult.put("error", e.getMessage());
                        results.put(file.getName(), fileResult);
                    }
                }
            }
            
            response.put("success", true);
            response.put("message", "批量解析完成");
            response.put("totalFiles", totalCount);
            response.put("successFiles", successCount);
            response.put("results", results);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量解析测试文件失败", e);
            response.put("success", false);
            response.put("message", "批量解析失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取支持的文件类型
     * 
     * @return 支持的文件类型列表
     */
    @GetMapping("/supported-types")
    public ResponseEntity<Map<String, Object>> getSupportedFileTypes() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("supportedTypes", fileParsingService.getSupportedFileTypes());
        return ResponseEntity.ok(response);
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "healthy");
        response.put("service", "file-parsing-service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 保存上传的文件
     * 
     * @param file 上传的文件
     * @return 保存的文件路径
     * @throws IOException 保存失败
     */
    private String saveUploadedFile(MultipartFile file) throws IOException {
        // 创建上传目录
        Path uploadPath = Paths.get(UPLOAD_DIR);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = timestamp + "_" + originalFilename;
        
        // 保存文件
        Path filePath = uploadPath.resolve(fileName);
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        
        return filePath.toAbsolutePath().toString();
    }

    /**
     * 检查是否为支持的文件类型
     * 
     * @param fileName 文件名
     * @return 是否支持
     */
    private boolean isSupportedFileType(String fileName) {
        if (fileName == null) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".docx") || 
               lowerFileName.endsWith(".doc") || 
               lowerFileName.endsWith(".pdf") || 
               lowerFileName.endsWith(".xlsx") || 
               lowerFileName.endsWith(".xls");
    }
}