# 设备识别规则配置文件
# 用于配置化管理设备类别、前缀映射等规则

# 设备类别映射（按设备ID千位数）
device_categories:
  0: "通用设备"      # 000x
  1: "泵"           # 1xxx  
  2: "过滤器"        # 2xxx
  3: "加热器"        # 3xxx
  4: "阀门"         # 4xxx
  5: "换热器"        # 5xxx
  6: "分离器"        # 6xxx
  7: "储罐"         # 7xxx
  8: "报警装置"      # 8xxx
  9: "控制系统"      # 9xxx (细分规则见下方)

# 9xxx设备细分规则
control_system_rules:
  9000-9099: "报警装置"    # 报警按钮/声光报警
  9200-9299: "安全关断"    # ESD系统
  default: "控制系统"      # 其他控制设备

# 仪表前缀分类
instrument_prefixes:
  sensors:    # 传感器（检测类）
    - "PT"    # 压力变送器
    - "TT"    # 温度变送器  
    - "FT"    # 流量变送器
    - "LT"    # 液位变送器
    - "AT"    # 分析变送器
    - "MT"    # 湿度变送器
    - "ST"    # 速度变送器
    - "WT"    # 重量变送器
    - "HT"    # 硬度变送器
    - "CT"    # 电导率变送器
    - "DT"    # 密度变送器
    - "VT"    # 振动变送器
    - "PI"    # 压力指示器
    - "TI"    # 温度指示器
    - "FI"    # 流量指示器
    - "LI"    # 液位指示器
    - "AI"    # 分析指示器
    - "MI"    # 湿度指示器
    - "SI"    # 速度指示器
    - "WI"    # 重量指示器
    - "HI"    # 硬度指示器
    - "CI"    # 电导率指示器
    - "DI"    # 密度指示器
    - "VI"    # 振动指示器
    - "GT"    # 气体检测器
    - "RS"    # RS485采集
    - "DS"    # 差压开关

  actuators:  # 执行器（控制类）
    - "XC"    # 控制阀
    - "XZ"    # 调节阀
    - "XY"    # 电磁阀
    - "XV"    # 球阀
    - "XS"    # 开关阀
    - "XA"    # 安全阀
    - "XM"    # 马达阀
    - "XH"    # 加热阀
    - "XL"    # 冷却阀
    - "XF"    # 过滤阀
    - "XW"    # 排水阀
    - "XE"    # 排气阀
    - "XO"    # 输出控制
    - "AO"    # 模拟输出
    - "DO"    # 数字输出
    - "MO"    # 马达输出
    - "SO"    # 开关输出
    - "HO"    # 加热输出
    - "CO"    # 冷却输出
    - "FO"    # 风扇输出
    - "LO"    # 照明输出
    - "TO"    # 定时输出
    - "WO"    # 报警输出
    - "VO"    # 阀门输出
    - "EXC"   # ESD输出控制
    - "HSC"   # 手动控制

  status:     # 状态信号（指示类）
    - "UA"    # 报警指示
    - "UH"    # 高报警
    - "UL"    # 低报警
    - "UF"    # 故障指示
    - "UT"    # 跳闸指示
    - "UP"    # 压力报警
    - "US"    # 安全报警
    - "UM"    # 维护报警
    - "UW"    # 警告指示
    - "UV"    # 阀门报警
    - "UC"    # 通信报警
    - "UD"    # 设备报警
    - "UIA"   # 综合报警
    - "ZS"    # 位置开关
    - "ZT"    # 温度开关
    - "ZL"    # 液位开关
    - "ZH"    # 高位开关
    - "ZF"    # 流量开关
    - "ZP"    # 压力开关
    - "ZA"    # 分析开关
    - "ZM"    # 马达开关
    - "ZW"    # 重量开关
    - "ZV"    # 阀门开关
    - "ZC"    # 通信开关
    - "ZD"    # 设备开关
    - "ZSH"   # 高位开关
    - "ZSL"   # 低位开关
    - "ZI"    # 位置指示
    - "PVXS"  # 阀位开关
    - "PVUA"  # 阀位报警
    - "XI"    # 位置指示
    - "SH"    # 手动开关
    - "SD"    # 设备开关

  alarms:     # 报警设备
    - "HS"    # 手动开关/报警按钮
    - "AA"    # 声光报警器

  communication: # 通信设备
    - "RS"    # RS485通信

# 表尾过滤关键词
table_footer_filters:
  - "ESD"
  - "RS485"
  - "通信"
  - "接口"
  - "说明"
  - "备注"
  - "注释"

# 语义绑定配置
semantic_binding:
  similarity_threshold: 0.75    # 相似度阈值
  cache_size: 1000             # 缓存大小
  batch_size: 8                # 批处理大小

# 忽略规则配置
ignore_rules:
  # 忽略的Tag正则表达式（测试数据、调试数据等）
  tag_regex: "^UNKNOWN_.*$|^UNKNOWN_DEVICE$"

  # 元数据行关键词
  metadata_keywords:
    - "文件号"
    - "项目号"
    - "阶段："
    - "日期："
    - "审核"
    - "设计"

  # 子表头行模式
  subheader_patterns:
    - "仪表位号.*检测点名称"
    - "位号.*描述"
    - "仪表位号.*点位描述"

# 设备关键词映射（用于语义匹配）
device_keywords:
  pump: ["泵", "pump", "增压", "循环", "输送"]
  valve: ["阀", "valve", "调压", "控制", "开关"]
  filter: ["过滤", "filter", "净化", "分离"]
  heater: ["加热", "heater", "升温", "热交换"]
  alarm: ["报警", "alarm", "警告", "故障"]
  sensor: ["检测", "sensor", "监测", "测量", "变送"]
  actuator: ["控制", "actuator", "执行", "驱动"]
