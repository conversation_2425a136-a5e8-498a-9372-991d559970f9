import org.apache.poi.xwpf.usermodel.*;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

public class TestWordParser {
    public static void main(String[] args) {
        String[] testFiles = {
            "test_files/7 滨江调压站IO表.docx",
            "test_files/7 路口铺门站IO表.docx"
        };
        
        for (String filePath : testFiles) {
            System.out.println("\n=== 分析文件: " + filePath + " ===");
            analyzeWordDocument(filePath);
        }
    }
    
    public static void analyzeWordDocument(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(fis)) {
            
            List<XWPFTable> tables = document.getTables();
            System.out.println("文档中共有 " + tables.size() + " 个表格");
            
            for (int tableIndex = 0; tableIndex < tables.size(); tableIndex++) {
                XWPFTable table = tables.get(tableIndex);
                System.out.println("\n--- 表格 " + (tableIndex + 1) + " ---");
                
                List<XWPFTableRow> rows = table.getRows();
                System.out.println("表格行数: " + rows.size());
                
                // 分析前10行
                int maxRows = Math.min(10, rows.size());
                for (int rowIndex = 0; rowIndex < maxRows; rowIndex++) {
                    XWPFTableRow row = rows.get(rowIndex);
                    List<XWPFTableCell> cells = row.getTableCells();
                    
                    System.out.print("第" + (rowIndex + 1) + "行 (" + cells.size() + "列): ");
                    
                    for (int cellIndex = 0; cellIndex < Math.min(6, cells.size()); cellIndex++) {
                        String cellText = getCellText(cells.get(cellIndex));
                        System.out.print("[" + cellIndex + ":" + cellText.trim() + "] ");
                    }
                    System.out.println();
                }
                
                if (rows.size() > 10) {
                    System.out.println("... 还有 " + (rows.size() - 10) + " 行");
                }
            }
            
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
        }
    }
    
    private static String getCellText(XWPFTableCell cell) {
        if (cell == null) {
            return "";
        }
        
        StringBuilder text = new StringBuilder();
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            text.append(paragraph.getText());
        }
        
        return text.toString().trim();
    }
}