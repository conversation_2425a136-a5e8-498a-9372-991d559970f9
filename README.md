# 设计院资料识别系统

这是一个用于识别和处理设计院相关文档的Java应用程序。

## 项目结构

```
设计院资料识别demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           └── demo/
│   │   │               ├── App.java                          # 主应用程序
│   │   │               └── DocumentRecognitionService.java   # 文档识别服务
│   │   └── resources/
│   │       └── logback.xml                                   # 日志配置
│   └── test/
│       └── java/
│           └── com/
│               └── example/
│                   └── demo/
│                       ├── AppTest.java                      # 主程序测试
│                       └── DocumentRecognitionServiceTest.java # 服务测试
├── pom.xml                                                   # Maven配置文件
└── README.md                                                 # 项目说明
```

## 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本（可选，也可以直接使用javac编译）

## 快速开始

### 使用Maven（推荐）

1. 编译项目：
   ```bash
   mvn compile
   ```

2. 运行测试：
   ```bash
   mvn test
   ```

3. 运行应用程序：
   ```bash
   mvn exec:java -Dexec.mainClass="com.example.demo.App"
   ```

4. 打包项目：
   ```bash
   mvn package
   ```

### 直接使用javac

1. 编译主程序：
   ```bash
   javac -cp "src/main/java" -d target/classes src/main/java/com/example/demo/*.java
   ```

2. 运行程序：
   ```bash
   java -cp target/classes com.example.demo.App
   ```

## 功能特性

- 文档类型识别（PDF、Word、图片等）
- 文档内容提取
- 文档结构分析
- 完整的日志记录
- 单元测试覆盖

## 开发说明

- 主要业务逻辑在 `DocumentRecognitionService` 类中
- 日志配置文件位于 `src/main/resources/logback.xml`
- 测试用例位于 `src/test/java` 目录下

## 待实现功能

- [ ] PDF文档解析
- [ ] Word文档解析
- [ ] 图片OCR识别
- [ ] 文档分类算法
- [ ] 批量处理功能
