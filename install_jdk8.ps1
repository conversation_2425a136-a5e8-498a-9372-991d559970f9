# JDK 1.8 自动安装脚本
# 适用于 Windows 10/11 64位系统

Write-Host "🚀 开始安装 JDK 1.8..." -ForegroundColor Green
Write-Host ""

# 检查是否以管理员权限运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限来安装JDK" -ForegroundColor Red
    Write-Host "请右键点击PowerShell并选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 设置下载参数
$jdkVersion = "1.8.0_391"
$jdkUrl = "https://github.com/adoptium/temurin8-binaries/releases/download/jdk8u392-b08/OpenJDK8U-jdk_x64_windows_hotspot_8u392b08.msi"
$downloadPath = "$env:TEMP\OpenJDK8U-jdk_x64_windows_hotspot_8u392b08.msi"
$installPath = "C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot"

Write-Host "📋 安装信息:" -ForegroundColor Cyan
Write-Host "   - JDK版本: $jdkVersion"
Write-Host "   - 安装路径: $installPath"
Write-Host "   - 下载地址: $jdkUrl"
Write-Host ""

# 检查是否已安装JDK 1.8
Write-Host "🔍 检查是否已安装JDK 1.8..." -ForegroundColor Yellow
$existingJava = Get-Command java -ErrorAction SilentlyContinue
if ($existingJava) {
    $javaVersion = & java -version 2>&1 | Select-String "version"
    Write-Host "当前Java版本: $javaVersion" -ForegroundColor Gray
    
    if ($javaVersion -match "1\.8\.0") {
        Write-Host "✅ JDK 1.8 已经安装！" -ForegroundColor Green
        & java -version
        exit 0
    }
}

# 检查网络连接
Write-Host "🌐 检查网络连接..." -ForegroundColor Yellow
try {
    $testConnection = Test-NetConnection -ComputerName "github.com" -Port 443 -InformationLevel Quiet
    if (-not $testConnection) {
        throw "无法连接到下载服务器"
    }
    Write-Host "✅ 网络连接正常" -ForegroundColor Green
} catch {
    Write-Host "❌ 网络连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查网络连接后重试" -ForegroundColor Yellow
    pause
    exit 1
}

# 下载JDK 1.8安装包
Write-Host "📥 下载JDK 1.8安装包..." -ForegroundColor Yellow
Write-Host "   下载路径: $downloadPath"

try {
    # 使用进度条显示下载进度
    $webClient = New-Object System.Net.WebClient
    $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
        $percent = $Event.SourceEventArgs.ProgressPercentage
        Write-Progress -Activity "下载JDK 1.8" -Status "已完成 $percent%" -PercentComplete $percent
    } | Out-Null
    
    $webClient.DownloadFile($jdkUrl, $downloadPath)
    $webClient.Dispose()
    Write-Progress -Activity "下载JDK 1.8" -Completed
    
    Write-Host "✅ 下载完成!" -ForegroundColor Green
    
    # 验证下载的文件
    if (Test-Path $downloadPath) {
        $fileSize = (Get-Item $downloadPath).Length / 1MB
        Write-Host "   文件大小: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Gray
    } else {
        throw "下载的文件不存在"
    }
    
} catch {
    Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 备用下载方式
    Write-Host "🔄 尝试备用下载方式..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $jdkUrl -OutFile $downloadPath -UseBasicParsing
        Write-Host "✅ 备用下载成功!" -ForegroundColor Green
    } catch {
        Write-Host "❌ 备用下载也失败了" -ForegroundColor Red
        Write-Host "请手动下载JDK 1.8: https://adoptium.net/temurin/releases/?version=8" -ForegroundColor Yellow
        pause
        exit 1
    }
}

# 安装JDK 1.8
Write-Host ""
Write-Host "🔧 开始安装JDK 1.8..." -ForegroundColor Yellow
Write-Host "   这可能需要几分钟时间，请耐心等待..."

try {
    # 静默安装MSI包
    $installArgs = @(
        "/i", 
        "`"$downloadPath`"",
        "/quiet",
        "/norestart",
        "ADDLOCAL=FeatureMain,FeatureEnvironment,FeatureJarFileRunWith,FeatureJavaHome",
        "INSTALLDIR=`"$installPath`""
    )
    
    Write-Host "执行安装命令..." -ForegroundColor Gray
    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ JDK 1.8 安装成功!" -ForegroundColor Green
    } else {
        throw "安装失败，退出代码: $($process.ExitCode)"
    }
    
} catch {
    Write-Host "❌ 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请尝试手动安装下载的MSI文件: $downloadPath" -ForegroundColor Yellow
    pause
    exit 1
}

# 配置环境变量
Write-Host ""
Write-Host "⚙️  配置环境变量..." -ForegroundColor Yellow

try {
    # 设置JAVA_HOME
    $javaHomePath = $installPath
    if (-not (Test-Path $javaHomePath)) {
        # 尝试查找实际安装路径
        $possiblePaths = @(
            "C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot",
            "C:\Program Files\AdoptOpenJDK\jdk-8.0.392.8-hotspot",
            "C:\Program Files\Java\jdk1.8.0_392"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                $javaHomePath = $path
                break
            }
        }
    }
    
    if (Test-Path $javaHomePath) {
        Write-Host "设置 JAVA_HOME = $javaHomePath" -ForegroundColor Gray
        [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, [EnvironmentVariableTarget]::Machine)
        
        # 添加到PATH
        $currentPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)
        $javaBinPath = "$javaHomePath\bin"
        
        if ($currentPath -notmatch [regex]::Escape($javaBinPath)) {
            $newPath = "$javaBinPath;$currentPath"
            Write-Host "添加到 PATH: $javaBinPath" -ForegroundColor Gray
            [Environment]::SetEnvironmentVariable("Path", $newPath, [EnvironmentVariableTarget]::Machine)
        }
        
        Write-Host "✅ 环境变量配置成功!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  警告: 找不到JDK安装路径，请手动配置环境变量" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 环境变量配置失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动设置JAVA_HOME和PATH环境变量" -ForegroundColor Yellow
}

# 清理下载文件
Write-Host ""
Write-Host "🧹 清理临时文件..." -ForegroundColor Yellow
try {
    Remove-Item $downloadPath -Force -ErrorAction SilentlyContinue
    Write-Host "✅ 清理完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️  无法删除临时文件: $downloadPath" -ForegroundColor Yellow
}

# 验证安装
Write-Host ""
Write-Host "🔍 验证JDK 1.8安装..." -ForegroundColor Yellow
Write-Host "请重新打开命令提示符或PowerShell窗口以刷新环境变量" -ForegroundColor Cyan

try {
    # 刷新当前会话的环境变量
    $env:JAVA_HOME = [Environment]::GetEnvironmentVariable("JAVA_HOME", [EnvironmentVariableTarget]::Machine)
    $env:Path = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)
    
    if ($env:JAVA_HOME -and (Test-Path "$env:JAVA_HOME\bin\java.exe")) {
        Write-Host "✅ JAVA_HOME 配置正确: $env:JAVA_HOME" -ForegroundColor Green
        
        # 测试Java版本
        $javaExe = "$env:JAVA_HOME\bin\java.exe"
        $versionOutput = & $javaExe -version 2>&1
        Write-Host ""
        Write-Host "Java版本信息:" -ForegroundColor Cyan
        $versionOutput | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
        
        if ($versionOutput -match "1\.8\.0") {
            Write-Host ""
            Write-Host "🎉 JDK 1.8 安装完成！" -ForegroundColor Green
            Write-Host ""
            Write-Host "📝 后续步骤:" -ForegroundColor Cyan
            Write-Host "   1. 重新打开命令提示符或PowerShell" -ForegroundColor White
            Write-Host "   2. 运行 'java -version' 验证安装" -ForegroundColor White
            Write-Host "   3. 返回项目目录重新启动您的应用" -ForegroundColor White
        } else {
            Write-Host "⚠️  警告: Java版本不是1.8" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  警告: JAVA_HOME配置可能有问题" -ForegroundColor Yellow
        Write-Host "请手动检查环境变量配置" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "⚠️  无法验证安装，请手动检查" -ForegroundColor Yellow
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "安装脚本执行完成！" -ForegroundColor Green
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")