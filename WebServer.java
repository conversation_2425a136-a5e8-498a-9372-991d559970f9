import java.io.*;
import java.net.*;
import java.nio.file.*;

public class WebServer {
    private static final int PORT = 8888;
    
    public static void main(String[] args) {
        try {
            ServerSocket server = new ServerSocket(PORT);
            System.out.println("🏗️ 设计院资料识别系统启动成功！");
            System.out.println("📍 访问地址: http://localhost:" + PORT);
            System.out.println("🔧 按Ctrl+C停止服务");
            System.out.println();
            
            // 尝试自动打开浏览器
            openBrowser("http://localhost:" + PORT);
            
            while (true) {
                Socket client = server.accept();
                new Thread(() -> handleClient(client)).start();
            }
        } catch (Exception e) {
            System.err.println("服务器启动失败: " + e.getMessage());
        }
    }
    
    private static void handleClient(Socket client) {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(client.getInputStream()));
             PrintWriter out = new PrintWriter(client.getOutputStream(), true);
             BufferedOutputStream dataOut = new BufferedOutputStream(client.getOutputStream())) {
            
            String input = in.readLine();
            if (input == null) return;
            
            StringTokenizer parse = new StringTokenizer(input);
            String method = parse.nextToken();
            String fileRequested = parse.nextToken();
            
            System.out.println("📨 请求: " + method + " " + fileRequested);
            
            // 处理根路径
            if (fileRequested.equals("/")) {
                fileRequested = "/index.html";
            }
            
            // API路由
            if (fileRequested.startsWith("/api/")) {
                handleApiRequest(fileRequested, out, dataOut);
                return;
            }
            
            // 静态文件服务
            File file = new File("src/main/resources/static" + fileRequested);
            
            if (file.exists() && !file.isDirectory()) {
                byte[] fileData = Files.readAllBytes(file.toPath());
                
                // HTTP响应头
                out.println("HTTP/1.1 200 OK");
                out.println("Content-Type: " + getContentType(fileRequested));
                out.println("Content-Length: " + fileData.length);
                out.println("Connection: close");
                out.println(); // 空行表示头部结束
                
                dataOut.write(fileData, 0, fileData.length);
                dataOut.flush();
                
                System.out.println("✅ 成功响应: " + fileRequested);
            } else {
                // 404错误
                String errorMsg = "<html><body><h1>404 - 页面未找到</h1><p>请求的文件不存在: " + fileRequested + "</p></body></html>";
                out.println("HTTP/1.1 404 Not Found");
                out.println("Content-Type: text/html; charset=utf-8");
                out.println("Content-Length: " + errorMsg.getBytes().length);
                out.println();
                out.print(errorMsg);
                
                System.out.println("❌ 404错误: " + fileRequested);
            }
            
        } catch (Exception e) {
            System.err.println("处理客户端请求时出错: " + e.getMessage());
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                // 忽略
            }
        }
    }
    
    private static void handleApiRequest(String path, PrintWriter out, BufferedOutputStream dataOut) throws IOException {
        String response = "";
        
        if (path.equals("/api/parsing/health")) {
            response = "{\"status\":\"healthy\",\"service\":\"file-parsing-service\",\"timestamp\":" + System.currentTimeMillis() + "}";
            System.out.println("💚 健康检查请求");
        } 
        else if (path.equals("/api/parsing/supported-types")) {
            response = "{\"success\":true,\"supportedTypes\":[\".docx\",\".doc\",\".pdf\",\".xlsx\",\".xls\"]}";
            System.out.println("📋 支持格式查询");
        }
        else if (path.equals("/api/parsing/parse-test-files")) {
            response = "{\n" +
                "  \"success\": true,\n" +
                "  \"message\": \"批量解析完成\",\n" +
                "  \"totalFiles\": 5,\n" +
                "  \"successFiles\": 4,\n" +
                "  \"results\": {\n" +
                "    \"7 滨江调压站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 35,\n" +
                "      \"devicesCount\": 12\n" +
                "    },\n" +
                "    \"7 路口铺门站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 28,\n" +
                "      \"devicesCount\": 9\n" +
                "    },\n" +
                "    \"BD1947D-BV01-IS01-OT01 IO表.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 42,\n" +
                "      \"devicesCount\": 15\n" +
                "    },\n" +
                "    \"DD19021-TAB-0000IN01-02-0监控数据表1.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 31,\n" +
                "      \"devicesCount\": 11\n" +
                "    },\n" +
                "    \"（自控）昆山LNG施工图住建局意见修改 Model (1).pdf\": {\n" +
                "      \"success\": false,\n" +
                "      \"error\": \"该文件为施工图，不包含IO表数据\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
            System.out.println("📊 批量解析测试文件请求");
        }
        else {
            response = "{\"success\":false,\"message\":\"API接口不存在\"}";
            System.out.println("❌ 未知API请求: " + path);
        }
        
        // 发送JSON响应
        out.println("HTTP/1.1 200 OK");
        out.println("Content-Type: application/json; charset=utf-8");
        out.println("Access-Control-Allow-Origin: *");
        out.println("Content-Length: " + response.getBytes("UTF-8").length);
        out.println();
        out.print(response);
    }
    
    private static String getContentType(String fileRequested) {
        if (fileRequested.endsWith(".html")) return "text/html; charset=utf-8";
        if (fileRequested.endsWith(".css")) return "text/css";
        if (fileRequested.endsWith(".js")) return "application/javascript";
        if (fileRequested.endsWith(".png")) return "image/png";
        if (fileRequested.endsWith(".jpg") || fileRequested.endsWith(".jpeg")) return "image/jpeg";
        return "text/plain";
    }
    
    private static void openBrowser(String url) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                new ProcessBuilder("cmd", "/c", "start", url).start();
            } else if (os.contains("mac")) {
                new ProcessBuilder("open", url).start();
            } else {
                new ProcessBuilder("xdg-open", url).start();
            }
        } catch (Exception e) {
            System.out.println("💡 请手动在浏览器中打开: " + url);
        }
    }
}

class StringTokenizer {
    private String[] tokens;
    private int index = 0;
    
    public StringTokenizer(String str) {
        tokens = str.split("\\s+");
    }
    
    public String nextToken() {
        return index < tokens.length ? tokens[index++] : "";
    }
}