package com.example.demo.parser;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 表格列角色枚举
 * 用于语义化识别表头列的功能
 */
public enum ColumnRole {
    TAG("仪表位号", Arrays.asList("仪表位号", "位号", "tag", "编号", "代号", "标识", "序号")),
    DESCRIPTION("点位描述", Arrays.asList("点位描述", "信号名称", "描述", "说明", "功能", "名称", "description", "检测点名称", "点名称")),
    SIGNAL_TYPE("信号类型", Arrays.asList("信号类型", "类型", "type", "ai", "di", "ao", "do", "模拟", "数字")),
    SIGNAL_RANGE("信号范围", Arrays.asList("信号范围", "量程", "范围", "range", "测量范围")),
    DATA_RANGE("数据范围", Arrays.asList("数据范围", "数值范围", "工程量范围")),
    UNIT("单位", Arrays.asList("单位", "unit", "工程单位", "量纲")),
    POWER("供电", Arrays.asList("现场仪表供电", "供电", "电源", "power", "供电方式")),
    ISOLATION("隔离", Arrays.asList("隔离", "isolation", "隔离方式", "隔离器")),
    REMARKS("备注", Arrays.asList("备注", "注释", "说明", "remarks", "comment"));

    private final String displayName;
    private final List<String> prototypes;

    ColumnRole(String displayName, List<String> prototypes) {
        this.displayName = displayName;
        this.prototypes = prototypes;
    }

    public String getDisplayName() {
        return displayName;
    }

    public List<String> getPrototypes() {
        return prototypes;
    }

    /**
     * 获取所有列角色的原型词表
     * 
     * @return 列角色到原型词列表的映射
     */
    public static Map<ColumnRole, List<String>> getAllPrototypes() {
        Map<ColumnRole, List<String>> map = new java.util.EnumMap<>(ColumnRole.class);
        for (ColumnRole role : values()) {
            map.put(role, role.getPrototypes());
        }
        return map;
    }

    /**
     * 判断是否为核心列（必须识别的列）
     * 
     * @return 是否为核心列
     */
    public boolean isCore() {
        return this == TAG || this == DESCRIPTION || this == SIGNAL_TYPE;
    }

    /**
     * 获取核心列角色
     * 
     * @return 核心列角色列表
     */
    public static List<ColumnRole> getCoreRoles() {
        return Arrays.asList(TAG, DESCRIPTION, SIGNAL_TYPE);
    }

    /**
     * 获取所有列角色
     * 
     * @return 所有列角色列表
     */
    public static List<ColumnRole> getAllRoles() {
        return Arrays.asList(values());
    }
}
