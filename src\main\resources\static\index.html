<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计院资料识别系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #999;
            font-size: 0.9em;
        }
        
        #fileInput {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .result-summary {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }
        
        .summary-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            flex: 1;
            margin: 0 10px;
        }
        
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .summary-label {
            color: #666;
            margin-top: 5px;
        }
        
        .result-tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            border-radius: 5px;
            margin-right: 10px;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .test-buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        .print-section {
            margin-top: 20px;
            text-align: center;
        }
        
        .print-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .print-preview {
            display: none;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 2px solid #667eea;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .print-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 5px;
        }
        
        .print-subtitle {
            color: #666;
            font-size: 1em;
        }
        
        .print-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .print-summary-item {
            text-align: center;
        }
        
        .print-summary-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .print-summary-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .print-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 0.9em;
        }
        
        .print-table th,
        .print-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        
        .print-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .print-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .print-section-title {
            font-size: 1.3em;
            color: #333;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .print-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }
        
        /* 层次结构显示样式 */
        .hierarchy-container {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .file-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .file-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
        }
        
        .file-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .file-title {
            font-size: 1.1em;
        }
        
        .file-stats {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
        }
        
        .file-content {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .file-content.expanded {
            max-height: 2000px;
            padding: 20px;
        }
        
        .device-section {
            margin-bottom: 25px;
            border-left: 3px solid #667eea;
            background: #f8f9ff;
            border-radius: 0 8px 8px 0;
        }
        
        .device-header {
            background: #f0f2ff;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e8ebf7;
        }
        
        .device-header:hover {
            background: #e8ebf7;
        }
        
        .device-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .device-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        
        .device-icon.sensor {
            background: #28a745;
        }
        
        .device-icon.actuator {
            background: #dc3545;
        }
        
        .device-icon.status {
            background: #ffc107;
            color: #333;
        }
        
        .device-icon.alarm {
            background: #fd7e14;
        }
        
        .device-icon.comm {
            background: #6f42c1;
        }
        
        .device-name {
            font-weight: bold;
            color: #333;
        }
        
        .device-type {
            color: #666;
            font-size: 0.9em;
        }
        
        .device-stats {
            font-size: 0.85em;
            color: #666;
        }
        
        .iopoints-container {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .iopoints-container.expanded {
            max-height: 1000px;
            padding: 15px;
        }
        
        .iopoint-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #28a745;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .iopoint-tag {
            font-weight: bold;
            color: #333;
            min-width: 100px;
            font-family: 'Courier New', monospace;
        }
        
        .iopoint-desc {
            flex: 1;
            color: #555;
            margin: 0 15px;
        }
        
        .iopoint-details {
            display: flex;
            gap: 10px;
            font-size: 0.85em;
        }
        
        .iopoint-signal {
            padding: 2px 6px;
            background: #e9ecef;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .iopoint-signal.AI {
            background: #d4edda;
            color: #155724;
        }
        
        .iopoint-signal.AO {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .iopoint-signal.DI {
            background: #f8d7da;
            color: #721c24;
        }
        
        .iopoint-signal.DO {
            background: #fff3cd;
            color: #856404;
        }
        
        .iopoint-unit {
            color: #666;
        }
        
        .iopoint-range {
            color: #666;
        }
        
        .expand-icon {
            transition: transform 0.3s ease;
        }
        
        .expand-icon.expanded {
            transform: rotate(90deg);
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .summary-badge {
            background: rgba(0,0,0,0.1);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        @media print {
            body * {
                visibility: hidden;
            }
            .print-preview,
            .print-preview * {
                visibility: visible;
            }
            .print-preview {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                margin: 0;
                padding: 20px;
                background: white !important;
            }
            .print-btn {
                display: none !important;
            }
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 设计院资料识别系统</h1>
            <p>智能解析工程文件中的设备和IO点位信息</p>
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📁</div>
            <div class="upload-text">点击选择文件或拖拽文件到此处</div>
            <div class="upload-hint">支持格式：.docx, .doc, .pdf, .xlsx, .xls</div>
            <input type="file" id="fileInput" accept=".docx,.doc,.pdf,.xlsx,.xls">
        </div>
        
        <div class="test-buttons">
            <button class="btn" onclick="testHealth()">🔍 健康检查</button>
            <button class="btn" onclick="parseTestFiles()">📊 解析测试文件</button>
            <button class="btn" onclick="getSupportedTypes()">📋 支持格式</button>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在解析文件，请稍候...</p>
        </div>
        
        <div class="result-area" id="resultArea">
            <div class="result-summary" id="resultSummary">
                <div class="summary-item">
                    <div class="summary-number" id="ioPointsCount">0</div>
                    <div class="summary-label">IO点位</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="devicesCount">0</div>
                    <div class="summary-label">设备数量</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="fileType">-</div>
                    <div class="summary-label">文件类型</div>
                </div>
            </div>
            
            <div class="result-tabs">
                <button class="tab active" onclick="showTab('hierarchy')">📁 层次结构</button>
                <button class="tab" onclick="showTab('iopoints')">📍 IO点位</button>
                <button class="tab" onclick="showTab('devices')">🔧 设备列表</button>
                <button class="tab" onclick="showTab('raw')">📄 原始数据</button>
            </div>
            
            <div class="tab-content" id="tabContent">
                <!-- 动态内容 -->
            </div>
            
            <!-- 打印功能区域 -->
            <div class="print-section">
                <button class="print-btn" onclick="generatePrintPreview()">📋 生成打印预览</button>
                <button class="print-btn" onclick="printResults()">🖨️ 打印解析结果</button>
                <button class="print-btn" onclick="exportToExcel()">📊 导出到Excel</button>
                <button class="print-btn" onclick="downloadUnifiedExcel()">📋 下载统一Excel表格</button>
                <button class="print-btn" onclick="exportToWord()">📄 导出到Word</button>
            </div>
        </div>
        
        <!-- 打印预览区域 -->
        <div class="print-preview" id="printPreview">
            <!-- 打印内容将在这里动态生成 -->
        </div>
    </div>

    <script>
        let currentData = null;
        let currentTab = 'iopoints';
        
        // 文件拖拽功能
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });
        
        // 文件上传处理
        function handleFileUpload(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            showLoading(true);
            hideMessages();
            
            fetch('/api/parsing/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showSuccess('文件解析成功！');
                    displayResults(data);
                } else {
                    showError('解析失败：' + data.message);
                }
            })
            .catch(error => {
                showLoading(false);
                showError('网络错误：' + error.message);
            });
        }
        
        // 健康检查
        function testHealth() {
            fetch('/api/parsing/health')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'healthy') {
                    showSuccess('服务状态正常！');
                } else {
                    showError('服务状态异常');
                }
            })
            .catch(error => {
                showError('连接失败，请检查服务是否启动');
            });
        }
        
        // 解析测试文件
        function parseTestFiles() {
            showLoading(true);
            hideMessages();
            
            fetch('/api/parsing/parse-test-files', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                if (data.success) {
                    showSuccess(`批量解析完成！成功解析 ${data.successFiles}/${data.totalFiles} 个文件`);
                    displayBatchResults(data);
                } else {
                    showError('批量解析失败：' + data.message);
                }
            })
            .catch(error => {
                showLoading(false);
                showError('网络错误：' + error.message);
            });
        }
        
        // 获取支持格式
        function getSupportedTypes() {
            fetch('/api/parsing/supported-types')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('支持的文件格式：' + data.supportedTypes.join(', '));
                }
            })
            .catch(error => {
                showError('获取格式信息失败');
            });
        }
        
        // 显示结果
        function displayResults(data) {
            currentData = data;
            document.getElementById('ioPointsCount').textContent = data.ioPointsCount || 0;
            document.getElementById('devicesCount').textContent = data.devicesCount || 0;
            document.getElementById('fileType').textContent = data.fileType || '-';
            
            document.getElementById('resultArea').style.display = 'block';
            showTab(currentTab);
        }
        
        // 显示批量结果
        function displayBatchResults(data) {
            let totalIoPoints = 0;
            let totalDevices = 0;
            
            Object.values(data.results).forEach(result => {
                if (result.success) {
                    totalIoPoints += result.ioPointsCount || 0;
                    totalDevices += result.devicesCount || 0;
                }
            });
            
            document.getElementById('ioPointsCount').textContent = totalIoPoints;
            document.getElementById('devicesCount').textContent = totalDevices;
            document.getElementById('fileType').textContent = `批量(${data.totalFiles}个文件)`;
            
            currentData = data;
            document.getElementById('resultArea').style.display = 'block';
            showBatchTab();
        }
        
        // 切换标签页
        function showTab(tabName) {
            currentTab = tabName;
            
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const content = document.getElementById('tabContent');
            
            if (!currentData) return;
            
            switch(tabName) {
                case 'hierarchy':
                    content.innerHTML = generateHierarchyView(currentData);
                    break;
                case 'iopoints':
                    content.innerHTML = generateIoPointsTable(currentData.ioPoints || []);
                    break;
                case 'devices':
                    content.innerHTML = generateDevicesTable(currentData.devices || []);
                    break;
                case 'raw':
                    content.innerHTML = `<pre>${JSON.stringify(currentData, null, 2)}</pre>`;
                    break;
            }
        }
        
        // 生成层次结构视图
        function generateHierarchyView(data) {
            if (!data) return '<div class="empty-state">暂无数据</div>';
            
            let html = '<div class="hierarchy-container">';
            
            // 如果是批量解析结果
            if (data.results) {
                Object.entries(data.results).forEach(([fileName, result]) => {
                    html += generateFileSection(fileName, result);
                });
            } 
            // 如果是单个文件结果
            else if (data.ioPoints || data.devices) {
                html += generateFileSection('解析结果', data);
            }
            
            html += '</div>';
            return html;
        }
        
        // 生成文件段落
        function generateFileSection(fileName, result) {
            if (!result.success) {
                return `
                    <div class="file-section">
                        <div class="file-header" onclick="toggleFileContent('${sanitizeId(fileName)}')">
                            <div class="file-title">📄 ${fileName}</div>
                            <div class="file-stats">
                                <span class="summary-badge">❌ 解析失败</span>
                            </div>
                        </div>
                        <div class="file-content" id="content-${sanitizeId(fileName)}">
                            <div class="empty-state">
                                <p>❌ 解析失败</p>
                                <p>错误信息: ${result.error || '未知错误'}</p>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            const ioPoints = result.ioPoints || [];
            const devices = result.devices || [];
            const deviceMap = createDevicePointsMap(devices, ioPoints);
            
            let html = `
                <div class="file-section">
                    <div class="file-header" onclick="toggleFileContent('${sanitizeId(fileName)}')">
                        <div class="file-title">📄 ${fileName}</div>
                        <div class="file-stats">
                            <span class="summary-badge">🔧 ${devices.length} 设备</span>
                            <span class="summary-badge">📍 ${ioPoints.length} 点位</span>
                            <span class="expand-icon" id="icon-${sanitizeId(fileName)}">▶</span>
                        </div>
                    </div>
                    <div class="file-content" id="content-${sanitizeId(fileName)}">
            `;
            
            if (devices.length === 0 && ioPoints.length === 0) {
                html += '<div class="empty-state">暂无设备和点位数据</div>';
            } else {
                // 显示设备及其关联的点位
                devices.forEach(device => {
                    html += generateDeviceSection(device, deviceMap[device.deviceId] || []);
                });
                
                // 显示未分组的点位
                const ungroupedPoints = ioPoints.filter(point => 
                    !devices.some(device => device.deviceId === point.tag)
                );
                
                if (ungroupedPoints.length > 0) {
                    html += `
                        <div class="device-section">
                            <div class="device-header" onclick="toggleDevicePoints('ungrouped-${sanitizeId(fileName)}')">
                                <div class="device-info">
                                    <div class="device-icon sensor">📍</div>
                                    <div>
                                        <div class="device-name">未分组点位</div>
                                        <div class="device-type">Independent IO Points</div>
                                    </div>
                                </div>
                                <div class="device-stats">
                                    <span class="summary-badge">${ungroupedPoints.length} 个点位</span>
                                    <span class="expand-icon" id="icon-ungrouped-${sanitizeId(fileName)}">▶</span>
                                </div>
                            </div>
                            <div class="iopoints-container" id="points-ungrouped-${sanitizeId(fileName)}">
                                ${generateIoPointsList(ungroupedPoints)}
                            </div>
                        </div>
                    `;
                }
            }
            
            html += '</div></div>';
            return html;
        }
        
        // 生成设备段落
        function generateDeviceSection(device, relatedPoints) {
            const deviceTypeMap = {
                'SENSOR': { icon: '📊', class: 'sensor', name: '传感器' },
                'ACTUATOR': { icon: '⚙️', class: 'actuator', name: '执行器' },
                'STATUS': { icon: '📡', class: 'status', name: '状态设备' },
                'ALARM': { icon: '🚨', class: 'alarm', name: '报警设备' },
                'COMM': { icon: '📶', class: 'comm', name: '通信设备' }
            };
            
            const typeInfo = deviceTypeMap[device.deviceType] || { icon: '🔧', class: 'sensor', name: '未知设备' };
            const deviceId = sanitizeId(device.deviceId);
            
            return `
                <div class="device-section">
                    <div class="device-header" onclick="toggleDevicePoints('${deviceId}')">
                        <div class="device-info">
                            <div class="device-icon ${typeInfo.class}">${typeInfo.icon}</div>
                            <div>
                                <div class="device-name">${device.deviceId}</div>
                                <div class="device-type">${device.deviceName || typeInfo.name}</div>
                            </div>
                        </div>
                        <div class="device-stats">
                            <span class="summary-badge">${relatedPoints.length} 个点位</span>
                            <span class="expand-icon" id="icon-${deviceId}">▶</span>
                        </div>
                    </div>
                    <div class="iopoints-container" id="points-${deviceId}">
                        ${generateIoPointsList(relatedPoints)}
                    </div>
                </div>
            `;
        }
        
        // 生成IO点位列表
        function generateIoPointsList(points) {
            if (!points || points.length === 0) {
                return '<div class="empty-state">暂无关联的IO点位</div>';
            }
            
            let html = '';
            points.forEach(point => {
                html += `
                    <div class="iopoint-item">
                        <div class="iopoint-tag">${point.tag || '-'}</div>
                        <div class="iopoint-desc">${point.desc || '-'}</div>
                        <div class="iopoint-details">
                            <span class="iopoint-signal ${point.signalType || ''}">${point.signalType || '-'}</span>
                            <span class="iopoint-unit">${point.unit || '-'}</span>
                            <span class="iopoint-range">${point.range || '-'}</span>
                        </div>
                    </div>
                `;
            });
            
            return html;
        }
        
        // 创建设备与点位的映射关系
        function createDevicePointsMap(devices, ioPoints) {
            const deviceMap = {};
            
            devices.forEach(device => {
                // 通过设备ID匹配点位（基于位号前缀）
                const relatedPoints = ioPoints.filter(point => {
                    if (!point.tag) return false;
                    // 简单的匹配规则：如果点位标签包含设备ID，或者设备ID包含点位标签的前缀
                    return point.tag.startsWith(device.deviceId) || 
                           device.deviceId.startsWith(point.tag.split('-')[0]);
                });
                
                deviceMap[device.deviceId] = relatedPoints;
            });
            
            return deviceMap;
        }
        
        // 工具函数：清理ID用于DOM元素
        function sanitizeId(str) {
            return str.replace(/[^a-zA-Z0-9]/g, '_');
        }
        
        // 切换文件内容显示
        function toggleFileContent(fileId) {
            const content = document.getElementById(`content-${fileId}`);
            const icon = document.getElementById(`icon-${fileId}`);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                if (icon) icon.textContent = '▶';
            } else {
                content.classList.add('expanded');
                if (icon) icon.textContent = '▼';
            }
        }
        
        // 切换设备点位显示
        function toggleDevicePoints(deviceId) {
            const container = document.getElementById(`points-${deviceId}`);
            const icon = document.getElementById(`icon-${deviceId}`);
            
            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                if (icon) icon.textContent = '▶';
            } else {
                container.classList.add('expanded');
                if (icon) icon.textContent = '▼';
            }
        }
        
        // 显示批量结果标签页
        function showBatchTab() {
            const content = document.getElementById('tabContent');
            content.innerHTML = generateHierarchyView(currentData);
        }
        
        // 生成IO点位表格
        function generateIoPointsTable(ioPoints) {
            if (!ioPoints || ioPoints.length === 0) {
                return '<p>暂无IO点位数据</p>';
            }
            
            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>位号</th>
                            <th>描述</th>
                            <th>信号类型</th>
                            <th>单位</th>
                            <th>量程</th>
                            <th>源行号</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            ioPoints.forEach(point => {
                html += `
                    <tr>
                        <td>${point.tag || '-'}</td>
                        <td>${point.desc || '-'}</td>
                        <td>${point.signalType || '-'}</td>
                        <td>${point.unit || '-'}</td>
                        <td>${point.range || '-'}</td>
                        <td>${point.sourceRow || '-'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        // 生成设备表格
        function generateDevicesTable(devices) {
            if (!devices || devices.length === 0) {
                return '<p>暂无设备数据</p>';
            }
            
            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>设备类型</th>
                            <th>关联点位数</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            devices.forEach(device => {
                html += `
                    <tr>
                        <td>${device.deviceId || '-'}</td>
                        <td>${device.deviceName || '-'}</td>
                        <td>${device.deviceType || '-'}</td>
                        <td>${device.ioPoints ? device.ioPoints.length : 0}</td>
                        <td>${device.createdAt || '-'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
        
        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
        
        // 生成打印预览
        function generatePrintPreview() {
            if (!currentData) {
                showError('没有可打印的数据，请先解析文件');
                return;
            }
            
            const printPreview = document.getElementById('printPreview');
            const currentTime = new Date().toLocaleString('zh-CN');
            
            let printContent = `
                <div class="print-header">
                    <div class="print-title">设计院资料识别系统 - 解析报告</div>
                    <div class="print-subtitle">生成时间: ${currentTime}</div>
                </div>
                
                <div class="print-summary">
                    <div class="print-summary-item">
                        <div class="print-summary-number">${currentData.ioPointsCount || 0}</div>
                        <div class="print-summary-label">IO点位总数</div>
                    </div>
                    <div class="print-summary-item">
                        <div class="print-summary-number">${currentData.devicesCount || 0}</div>
                        <div class="print-summary-label">设备总数</div>
                    </div>
                    <div class="print-summary-item">
                        <div class="print-summary-number">${currentData.fileType || '-'}</div>
                        <div class="print-summary-label">文件类型</div>
                    </div>
                </div>
            `;
            
            // 添加IO点位表格
            if (currentData.ioPoints && currentData.ioPoints.length > 0) {
                printContent += `
                    <div class="print-section-title">📍 IO点位详细信息</div>
                    <table class="print-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>位号</th>
                                <th>描述</th>
                                <th>信号类型</th>
                                <th>单位</th>
                                <th>量程</th>
                                <th>源行号</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                currentData.ioPoints.forEach((point, index) => {
                    printContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${point.tag || '-'}</td>
                            <td>${point.desc || '-'}</td>
                            <td>${point.signalType || '-'}</td>
                            <td>${point.unit || '-'}</td>
                            <td>${point.range || '-'}</td>
                            <td>${point.sourceRow || '-'}</td>
                        </tr>
                    `;
                });
                
                printContent += `
                        </tbody>
                    </table>
                `;
            }
            
            // 添加设备列表表格
            if (currentData.devices && currentData.devices.length > 0) {
                printContent += `
                    <div class="print-section-title">🔧 设备统计信息</div>
                    <table class="print-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>设备ID</th>
                                <th>设备名称</th>
                                <th>设备类型</th>
                                <th>关联点位数</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                currentData.devices.forEach((device, index) => {
                    printContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${device.deviceId || '-'}</td>
                            <td>${device.deviceName || '-'}</td>
                            <td>${device.deviceType || '-'}</td>
                            <td>${device.ioPoints ? device.ioPoints.length : 0}</td>
                            <td>${device.createdAt || '-'}</td>
                        </tr>
                    `;
                });
                
                printContent += `
                        </tbody>
                    </table>
                `;
            }
            
            // 添加批量解析结果（如果有）
            if (currentData.results) {
                printContent += `
                    <div class="print-section-title">📊 批量解析结果</div>
                    <table class="print-table">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>解析状态</th>
                                <th>文件类型</th>
                                <th>IO点位数</th>
                                <th>设备数</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                Object.entries(currentData.results).forEach(([fileName, result]) => {
                    printContent += `
                        <tr>
                            <td>${fileName}</td>
                            <td>${result.success ? '✅ 成功' : '❌ 失败'}</td>
                            <td>${result.fileType || '-'}</td>
                            <td>${result.ioPointsCount || '-'}</td>
                            <td>${result.devicesCount || '-'}</td>
                            <td>${result.error || '正常'}</td>
                        </tr>
                    `;
                });
                
                printContent += `
                        </tbody>
                    </table>
                `;
            }
            
            printContent += `
                <div class="print-footer">
                    <p>此报告由设计院资料识别系统自动生成</p>
                    <p>系统版本: v1.0 | JDK版本: 1.8 | 生成时间: ${currentTime}</p>
                </div>
            `;
            
            printPreview.innerHTML = printContent;
            printPreview.style.display = 'block';
            
            // 滚动到打印预览区域
            printPreview.scrollIntoView({ behavior: 'smooth' });
            showSuccess('打印预览已生成！您可以查看下方的预览内容。');
        }
        
        // 打印解析结果
        function printResults() {
            if (!currentData) {
                showError('没有可打印的数据，请先解析文件');
                return;
            }
            
            // 先生成打印预览
            generatePrintPreview();
            
            // 延迟一点时间确保内容渲染完成
            setTimeout(() => {
                window.print();
            }, 500);
        }
        
        // 导出到Excel格式
        function exportToExcel() {
            if (!currentData) {
                showError('没有可导出的数据，请先解析文件');
                return;
            }
            
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // 添加概要信息
            csvContent += "设计院资料识别系统解析报告\\n";
            csvContent += "生成时间," + new Date().toLocaleString('zh-CN') + "\\n";
            csvContent += "文件类型," + (currentData.fileType || '-') + "\\n";
            csvContent += "IO点位总数," + (currentData.ioPointsCount || 0) + "\\n";
            csvContent += "设备总数," + (currentData.devicesCount || 0) + "\\n\\n";
            
            // 添加IO点位数据
            if (currentData.ioPoints && currentData.ioPoints.length > 0) {
                csvContent += "IO点位详细信息\\n";
                csvContent += "序号,位号,描述,信号类型,单位,量程,源行号\\n";
                
                currentData.ioPoints.forEach((point, index) => {
                    csvContent += [
                        index + 1,
                        point.tag || '-',
                        point.desc || '-',
                        point.signalType || '-',
                        point.unit || '-',
                        point.range || '-',
                        point.sourceRow || '-'
                    ].join(',') + '\\n';
                });
                csvContent += '\\n';
            }
            
            // 添加设备数据
            if (currentData.devices && currentData.devices.length > 0) {
                csvContent += "设备统计信息\\n";
                csvContent += "序号,设备ID,设备名称,设备类型,关联点位数,创建时间\\n";
                
                currentData.devices.forEach((device, index) => {
                    csvContent += [
                        index + 1,
                        device.deviceId || '-',
                        device.deviceName || '-',
                        device.deviceType || '-',
                        device.ioPoints ? device.ioPoints.length : 0,
                        device.createdAt || '-'
                    ].join(',') + '\\n';
                });
            }
            
            // 添加UTF-8 BOM以确保Excel正确识别中文
            const BOM = '\uFEFF';
            const csvWithBOM = BOM + csvContent.substr(csvContent.indexOf(',') + 1);
            
            // 创建Blob对象确保正确的UTF-8编码
            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            
            // 创建下载链接
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "解析结果_" + new Date().toISOString().slice(0,19).replace(/:/g, '-') + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            URL.revokeObjectURL(url);
            
            showSuccess('Excel文件已开始下载！');
        }
        
        // 下载统一的Excel表格（合并所有文件的数据）
        function downloadUnifiedExcel() {
            // 获取批量解析数据
            fetch('/api/parsing/parse-test-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    generateUnifiedExcel(data.results);
                } else {
                    showError('获取数据失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络请求失败，请检查服务器连接');
            });
        }
        
        // 生成统一的Excel文件
        function generateUnifiedExcel(results) {
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // 标题信息
            csvContent += "设计院资料识别系统 - 统一数据表格\\n";
            csvContent += "生成时间," + new Date().toLocaleString('zh-CN') + "\\n\\n";
            
            // 统计总览
            let totalFiles = Object.keys(results).length;
            let totalIoPoints = 0;
            let totalDevices = 0;
            
            Object.values(results).forEach(result => {
                if (result.success) {
                    totalIoPoints += result.ioPointsCount || 0;
                    totalDevices += result.devicesCount || 0;
                }
            });
            
            csvContent += "统计总览\\n";
            csvContent += "文件总数," + totalFiles + "\\n";
            csvContent += "IO点位总数," + totalIoPoints + "\\n";
            csvContent += "设备总数," + totalDevices + "\\n\\n";
            
            // 统一的IO点位表格
            csvContent += "统一IO点位表格\\n";
            csvContent += "序号,源文件,位号,描述,信号类型,单位,量程,所属设备,设备类型,源行号\\n";
            
            let pointIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                if (result.success && result.ioPoints) {
                    // 创建设备映射表
                    const deviceMap = {};
                    if (result.devices) {
                        result.devices.forEach(device => {
                            deviceMap[device.deviceId] = device;
                        });
                    }
                    
                    result.ioPoints.forEach(point => {
                        const device = deviceMap[point.deviceId];
                        csvContent += [
                            pointIndex++,
                            fileName,
                            point.tag || '-',
                            point.desc || '-',
                            point.signalType || '-',
                            point.unit || '-',
                            point.range || '-',
                            device ? device.deviceName : (point.deviceId || '-'),
                            device ? device.deviceType : '-',
                            point.sourceRow || '-'
                        ].join(',') + '\\n';
                    });
                }
            });
            
            csvContent += '\\n';
            
            // 统一设备表格
            csvContent += "统一设备表格\\n";
            csvContent += "序号,源文件,设备ID,设备名称,设备类型,关联IO点位数,创建时间\\n";
            
            let deviceIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                if (result.success && result.devices) {
                    result.devices.forEach(device => {
                        // 计算关联的IO点位数
                        const relatedPoints = result.ioPoints ? 
                            result.ioPoints.filter(point => point.deviceId === device.deviceId).length : 0;
                        
                        csvContent += [
                            deviceIndex++,
                            fileName,
                            device.deviceId || '-',
                            device.deviceName || '-',
                            device.deviceType || '-',
                            relatedPoints,
                            device.createdAt || '-'
                        ].join(',') + '\\n';
                    });
                }
            });
            
            csvContent += '\\n';
            
            // 文件解析状态表格
            csvContent += "文件解析状态\\n";
            csvContent += "序号,文件名,解析状态,文件类型,IO点位数,设备数,错误信息\\n";
            
            let fileIndex = 1;
            Object.entries(results).forEach(([fileName, result]) => {
                csvContent += [
                    fileIndex++,
                    fileName,
                    result.success ? '成功' : '失败',
                    result.fileType || '-',
                    result.ioPointsCount || 0,
                    result.devicesCount || 0,
                    result.error || '-'
                ].join(',') + '\\n';
            });
            
            // 添加UTF-8 BOM以确保Excel正确识别中文
            const BOM = '\uFEFF';
            const csvWithBOM = BOM + csvContent.substr(csvContent.indexOf(',') + 1);
            
            // 创建Blob对象确保正确的UTF-8编码
            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            
            // 创建下载链接
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "统一数据表格_" + new Date().toISOString().slice(0,19).replace(/:/g, '-') + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            URL.revokeObjectURL(url);
            
            showSuccess('统一Excel表格已开始下载！包含' + totalIoPoints + '个IO点位和' + totalDevices + '个设备');
        }
        
        // 导出到Word格式
        function exportToWord() {
            if (!currentData) {
                showError('没有可导出的数据，请先解析文件');
                return;
            }
            
            // 生成HTML内容用于Word导出
            let htmlContent = `
                <html>
                    <head>
                        <meta charset="utf-8">
                        <title>设计院资料识别系统解析报告</title>
                        <style>
                            body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; }
                            .header { text-align: center; margin-bottom: 30px; }
                            .title { font-size: 20px; font-weight: bold; margin-bottom: 10px; }
                            .subtitle { color: #666; }
                            .summary { display: flex; justify-content: space-around; margin: 20px 0; }
                            .summary-item { text-align: center; padding: 10px; }
                            .summary-number { font-size: 24px; font-weight: bold; color: #667eea; }
                            .summary-label { color: #666; margin-top: 5px; }
                            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                            th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: left; }
                            th { background: #f8f9fa; font-weight: bold; }
                            .section-title { font-size: 16px; font-weight: bold; margin: 25px 0 15px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <div class="title">设计院资料识别系统 - 解析报告</div>
                            <div class="subtitle">生成时间: ${new Date().toLocaleString('zh-CN')}</div>
                        </div>
                        
                        <div class="summary">
                            <div class="summary-item">
                                <div class="summary-number">${currentData.ioPointsCount || 0}</div>
                                <div class="summary-label">IO点位总数</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">${currentData.devicesCount || 0}</div>
                                <div class="summary-label">设备总数</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">${currentData.fileType || '-'}</div>
                                <div class="summary-label">文件类型</div>
                            </div>
                        </div>
            `;
            
            // 添加IO点位表格
            if (currentData.ioPoints && currentData.ioPoints.length > 0) {
                htmlContent += `
                    <div class="section-title">IO点位详细信息</div>
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th><th>位号</th><th>描述</th><th>信号类型</th><th>单位</th><th>量程</th><th>源行号</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                currentData.ioPoints.forEach((point, index) => {
                    htmlContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${point.tag || '-'}</td>
                            <td>${point.desc || '-'}</td>
                            <td>${point.signalType || '-'}</td>
                            <td>${point.unit || '-'}</td>
                            <td>${point.range || '-'}</td>
                            <td>${point.sourceRow || '-'}</td>
                        </tr>
                    `;
                });
                
                htmlContent += '</tbody></table>';
            }
            
            htmlContent += '</body></html>';
            
            // 创建Blob并下载
            const blob = new Blob([htmlContent], { type: 'application/msword' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '解析结果_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.doc';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            showSuccess('Word文件已开始下载！');
        }
        
        // 页面加载完成后自动检查服务状态
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>