import java.io.*;
import java.net.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;

/**
 * 简单的HTTP服务器
 * 用于测试Web界面
 */
public class SimpleServer {

    private static final int PORT = 8080;
    private static final String WEB_ROOT = "src/main/resources/static";
    
    public static void main(String[] args) {
        try {
            HttpServer server = HttpServer.create(new InetSocketAddress(PORT), 0);
            
            // 静态文件服务
            server.createContext("/", new StaticFileHandler());
            
            // API接口
            server.createContext("/api/parsing/health", new HealthHandler());
            server.createContext("/api/parsing/supported-types", new SupportedTypesHandler());
            server.createContext("/api/parsing/parse-test-files", new ParseTestFilesHandler());
            
            server.setExecutor(Executors.newFixedThreadPool(10));
            server.start();
            
            System.out.println("设计院资料识别系统启动成功！");
            System.out.println("访问地址: http://localhost:" + PORT);
            System.out.println("按Ctrl+C停止服务");
            
            // 自动打开浏览器
            try {
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("cmd /c start http://localhost:" + PORT);
                } else if (os.contains("mac")) {
                    Runtime.getRuntime().exec("open http://localhost:" + PORT);
                } else {
                    Runtime.getRuntime().exec("xdg-open http://localhost:" + PORT);
                }
            } catch (Exception e) {
                System.out.println("无法自动打开浏览器，请手动访问: http://localhost:" + PORT);
            }
            
        } catch (Exception e) {
            System.err.println("服务器启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 静态文件处理器
    static class StaticFileHandler implements HttpHandler {
        public void handle(HttpExchange exchange) throws IOException {
            String path = exchange.getRequestURI().getPath();
            if (path.equals("/") || path.equals("")) {
                path = "/index.html";
            }
            
            File file = new File(WEB_ROOT + path);
            if (file.exists() && !file.isDirectory()) {
                byte[] content = Files.readAllBytes(file.toPath());
                
                // 设置Content-Type
                String contentType = getContentType(path);
                exchange.getResponseHeaders().set("Content-Type", contentType);
                
                exchange.sendResponseHeaders(200, content.length);
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(content);
                }
            } else {
                String response = "404 - File Not Found";
                exchange.sendResponseHeaders(404, response.length());
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(response.getBytes());
                }
            }
        }
        
        private String getContentType(String path) {
            if (path.endsWith(".html")) return "text/html; charset=utf-8";
            if (path.endsWith(".css")) return "text/css";
            if (path.endsWith(".js")) return "application/javascript";
            if (path.endsWith(".png")) return "image/png";
            if (path.endsWith(".jpg") || path.endsWith(".jpeg")) return "image/jpeg";
            return "text/plain";
        }
    }
    
    // 健康检查处理器
    static class HealthHandler implements HttpHandler {
        public void handle(HttpExchange exchange) throws IOException {
            String response = "{\"status\":\"healthy\",\"service\":\"file-parsing-service\",\"timestamp\":" + System.currentTimeMillis() + "}";
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.length());
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }
    
    // 支持格式处理器
    static class SupportedTypesHandler implements HttpHandler {
        public void handle(HttpExchange exchange) throws IOException {
            String response = "{\"success\":true,\"supportedTypes\":[\".docx\",\".doc\",\".pdf\",\".xlsx\",\".xls\"]}";
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.length());
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }
    
    // 测试文件解析处理器
    static class ParseTestFilesHandler implements HttpHandler {
        public void handle(HttpExchange exchange) throws IOException {
            // 模拟解析结果
            String response = "{\n" +
                "  \"success\": true,\n" +
                "  \"message\": \"批量解析完成\",\n" +
                "  \"totalFiles\": 5,\n" +
                "  \"successFiles\": 4,\n" +
                "  \"results\": {\n" +
                "    \"7 滨江调压站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 35,\n" +
                "      \"devicesCount\": 12\n" +
                "    },\n" +
                "    \"7 路口铺门站IO表.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 28,\n" +
                "      \"devicesCount\": 9\n" +
                "    },\n" +
                "    \"BD1947D-BV01-IS01-OT01 IO表.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 42,\n" +
                "      \"devicesCount\": 15\n" +
                "    },\n" +
                "    \"DD19021-TAB-0000IN01-02-0监控数据表1.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 31,\n" +
                "      \"devicesCount\": 11\n" +
                "    },\n" +
                "    \"（自控）昆山LNG施工图住建局意见修改 Model (1).pdf\": {\n" +
                "      \"success\": false,\n" +
                "      \"error\": \"该文件为施工图，不包含IO表数据\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.length());
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }
}

// HttpServer和HttpHandler接口的简单实现
interface HttpHandler {
    void handle(HttpExchange exchange) throws IOException;
}

class HttpServer {
    private ServerSocket serverSocket;
    private boolean running;
    
    public static HttpServer create(InetSocketAddress addr, int backlog) throws IOException {
        HttpServer server = new HttpServer();
        server.serverSocket = new ServerSocket(addr.getPort());
        return server;
    }
    
    public HttpContext createContext(String path, HttpHandler handler) {
        return new HttpContext(path, handler);
    }
    
    public void setExecutor(java.util.concurrent.Executor executor) {
        // 简化实现
    }
    
    public void start() {
        running = true;
        new Thread(() -> {
            while (running) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    new Thread(() -> handleRequest(clientSocket)).start();
                } catch (IOException e) {
                    if (running) {
                        System.err.println("处理请求时出错: " + e.getMessage());
                    }
                }
            }
        }).start();
    }
    
    private void handleRequest(Socket clientSocket) {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {
            
            String requestLine = in.readLine();
            if (requestLine == null) return;
            
            String[] parts = requestLine.split(" ");
            if (parts.length >= 2) {
                String method = parts[0];
                String path = parts[1];
                
                // 简单的路由处理
                if (path.equals("/")) {
                    serveFile(out, "src/main/resources/static/index.html");
                } else if (path.startsWith("/api/parsing/health")) {
                    serveJson(out, "{\"status\":\"healthy\",\"service\":\"file-parsing-service\",\"timestamp\":" + System.currentTimeMillis() + "}");
                } else if (path.startsWith("/api/parsing/supported-types")) {
                    serveJson(out, "{\"success\":true,\"supportedTypes\":[\".docx\",\".doc\",\".pdf\",\".xlsx\",\".xls\"]}");
                } else {
                    out.println("HTTP/1.1 404 Not Found");
                    out.println("Content-Length: 0");
                    out.println();
                }
            }
        } catch (IOException e) {
            System.err.println("处理客户端请求时出错: " + e.getMessage());
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                // 忽略关闭错误
            }
        }
    }
    
    private void serveFile(PrintWriter out, String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                String content = new String(Files.readAllBytes(file.toPath()), "UTF-8");
                out.println("HTTP/1.1 200 OK");
                out.println("Content-Type: text/html; charset=utf-8");
                out.println("Content-Length: " + content.getBytes("UTF-8").length);
                out.println();
                out.print(content);
            } else {
                out.println("HTTP/1.1 404 Not Found");
                out.println("Content-Length: 0");
                out.println();
            }
        } catch (IOException e) {
            out.println("HTTP/1.1 500 Internal Server Error");
            out.println("Content-Length: 0");
            out.println();
        }
    }
    
    private void serveJson(PrintWriter out, String json) {
        out.println("HTTP/1.1 200 OK");
        out.println("Content-Type: application/json");
        out.println("Access-Control-Allow-Origin: *");
        out.println("Content-Length: " + json.getBytes().length);
        out.println();
        out.print(json);
    }
}

class HttpContext {
    private String path;
    private HttpHandler handler;
    
    public HttpContext(String path, HttpHandler handler) {
        this.path = path;
        this.handler = handler;
    }
}

class HttpExchange {
    // 简化实现
    public URI getRequestURI() { return null; }
    public Headers getResponseHeaders() { return new Headers(); }
    public void sendResponseHeaders(int code, long length) {}
    public OutputStream getResponseBody() { return null; }
}

class Headers {
    public void set(String name, String value) {}
}