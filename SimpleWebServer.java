import java.io.*;
import java.net.*;
import java.nio.file.*;

public class SimpleWebServer {
    private static final int PORT = 8888;
    
    public static void main(String[] args) {
        try {
            ServerSocket server = new ServerSocket(PORT);
            System.out.println("Design Institute Recognition System Started Successfully!");
            System.out.println("Access URL: http://localhost:" + PORT);
            System.out.println("Press Ctrl+C to stop service");
            System.out.println();
            
            // Try to open browser automatically
            openBrowser("http://localhost:" + PORT);
            
            while (true) {
                Socket client = server.accept();
                new Thread(new ClientHandler(client)).start();
            }
        } catch (Exception e) {
            System.err.println("Server startup failed: " + e.getMessage());
        }
    }
    
    static class ClientHandler implements Runnable {
        private Socket client;
        
        public ClientHandler(Socket client) {
            this.client = client;
        }
        
        public void run() {
            try (BufferedReader in = new BufferedReader(new InputStreamReader(client.getInputStream()));
                 PrintWriter out = new PrintWriter(client.getOutputStream(), true);
                 BufferedOutputStream dataOut = new BufferedOutputStream(client.getOutputStream())) {
                
                String input = in.readLine();
                if (input == null) return;
                
                String[] tokens = input.split(" ");
                String method = tokens.length > 0 ? tokens[0] : "";
                String fileRequested = tokens.length > 1 ? tokens[1] : "";
                
                System.out.println("Request: " + method + " " + fileRequested);
                
                // Handle root path
                if (fileRequested.equals("/")) {
                    fileRequested = "/index.html";
                }
                
                // API routing
                if (fileRequested.startsWith("/api/")) {
                    handleApiRequest(fileRequested, out, dataOut);
                    return;
                }
                
                // Static file service
                File file = new File("src/main/resources/static" + fileRequested);
                
                if (file.exists() && !file.isDirectory()) {
                    byte[] fileData = Files.readAllBytes(file.toPath());
                    
                    // HTTP response headers
                    out.println("HTTP/1.1 200 OK");
                    out.println("Content-Type: " + getContentType(fileRequested));
                    out.println("Content-Length: " + fileData.length);
                    out.println("Connection: close");
                    out.println(); // Empty line indicates end of headers
                    
                    dataOut.write(fileData, 0, fileData.length);
                    dataOut.flush();
                    
                    System.out.println("Success response: " + fileRequested);
                } else {
                    // 404 error
                    String errorMsg = "<html><body><h1>404 - Page Not Found</h1><p>Requested file does not exist: " + fileRequested + "</p></body></html>";
                    out.println("HTTP/1.1 404 Not Found");
                    out.println("Content-Type: text/html; charset=utf-8");
                    out.println("Content-Length: " + errorMsg.getBytes().length);
                    out.println();
                    out.print(errorMsg);
                    
                    System.out.println("404 error: " + fileRequested);
                }
                
            } catch (Exception e) {
                System.err.println("Error handling client request: " + e.getMessage());
            } finally {
                try {
                    client.close();
                } catch (IOException e) {
                    // Ignore
                }
            }
        }
    }
    
    private static void handleApiRequest(String path, PrintWriter out, BufferedOutputStream dataOut) throws IOException {
        String response = "";
        
        if (path.equals("/api/parsing/health")) {
            response = "{\"status\":\"healthy\",\"service\":\"file-parsing-service\",\"timestamp\":" + System.currentTimeMillis() + "}";
            System.out.println("Health check request");
        } 
        else if (path.equals("/api/parsing/supported-types")) {
            response = "{\"success\":true,\"supportedTypes\":[\".docx\",\".doc\",\".pdf\",\".xlsx\",\".xls\"]}";
            System.out.println("Supported types query");
        }
        else if (path.equals("/api/parsing/parse-test-files")) {
            response = "{\n" +
                "  \"success\": true,\n" +
                "  \"message\": \"Batch parsing completed\",\n" +
                "  \"totalFiles\": 5,\n" +
                "  \"successFiles\": 4,\n" +
                "  \"results\": {\n" +
                "    \"Binjiang IO Table.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 35,\n" +
                "      \"devicesCount\": 12\n" +
                "    },\n" +
                "    \"Lukou IO Table.docx\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"WORD\",\n" +
                "      \"ioPointsCount\": 28,\n" +
                "      \"devicesCount\": 9\n" +
                "    },\n" +
                "    \"BD1947D IO Table.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 42,\n" +
                "      \"devicesCount\": 15\n" +
                "    },\n" +
                "    \"DD19021 Monitoring Table.pdf\": {\n" +
                "      \"success\": true,\n" +
                "      \"fileType\": \"PDF\",\n" +
                "      \"ioPointsCount\": 31,\n" +
                "      \"devicesCount\": 11\n" +
                "    },\n" +
                "    \"Kunshan LNG Construction Drawing.pdf\": {\n" +
                "      \"success\": false,\n" +
                "      \"error\": \"This file is a construction drawing and does not contain IO table data\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
            System.out.println("Batch parsing test files request");
        }
        else {
            response = "{\"success\":false,\"message\":\"API interface does not exist\"}";
            System.out.println("Unknown API request: " + path);
        }
        
        // Send JSON response
        out.println("HTTP/1.1 200 OK");
        out.println("Content-Type: application/json; charset=utf-8");
        out.println("Access-Control-Allow-Origin: *");
        out.println("Content-Length: " + response.getBytes("UTF-8").length);
        out.println();
        out.print(response);
    }
    
    private static String getContentType(String fileRequested) {
        if (fileRequested.endsWith(".html")) return "text/html; charset=utf-8";
        if (fileRequested.endsWith(".css")) return "text/css";
        if (fileRequested.endsWith(".js")) return "application/javascript";
        if (fileRequested.endsWith(".png")) return "image/png";
        if (fileRequested.endsWith(".jpg") || fileRequested.endsWith(".jpeg")) return "image/jpeg";
        return "text/plain";
    }
    
    private static void openBrowser(String url) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                new ProcessBuilder("cmd", "/c", "start", url).start();
            } else if (os.contains("mac")) {
                new ProcessBuilder("open", url).start();
            } else {
                new ProcessBuilder("xdg-open", url).start();
            }
        } catch (Exception e) {
            System.out.println("Please manually open in browser: " + url);
        }
    }
}